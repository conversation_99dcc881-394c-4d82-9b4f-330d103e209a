version: '3.3'
services:
  app_website:
    image: registry.cn-hangzhou.aliyuncs.com/lank/nginx:fwebsite_nginx_1.0.1
    restart: always
    hostname: app_website
    container_name: app_website
    ports:
      - "30001:8686"
    volumes:
      - /opt/website/log:/var/log/nginx
      - /opt/website/release:/nginx/html/release
      - /app/app/bin:/nginx/html/do
      - /app/app/docker:/nginx/html/docker
#      - type: bind
#        source: /app/docker/website/html
#        target: /nginx/html
    user: root
    privileged: true
    networks:
      app_web_network:
        ipv4_address: ***********
networks:
  app_web_network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/24

#http://**************:30001/release/
#http://**************:30001/do/deployApp.sh
