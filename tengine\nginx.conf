worker_processes auto;
worker_rlimit_nofile 65535;
events {
    use epoll;
    multi_accept on;
    worker_connections 1024;
}
http {
    include       mime.types;
    default_type  application/octet-stream;
    log_format    main  '$remote_addr - $remote_user [$time_local] "$request" '
                  '$status $body_bytes_sent "$http_referer" '
                  '"$http_user_agent" "$http_x_forwarded_for"';
    access_log  /var/log/nginx/access.log main;
    error_log   /var/log/nginx/error.log warn;
    proxy_buffering on;
    proxy_buffer_size 8k;
    proxy_buffers 4 32k;
    proxy_busy_buffers_size 64K;
    proxy_temp_path /var/nginxtemp 1 2;
    proxy_max_temp_file_size 20m;
    proxy_temp_file_write_size 256k;
    #cache
    proxy_cache_path /var/nginxcache levels=1:1 keys_zone=cache_one:200m inactive=3d max_size=30g;
    server_tokens off;
    keepalive_timeout 60;
    keepalive_requests 1024;
    client_header_buffer_size 512k;
    client_body_buffer_size 1m;
    client_max_body_size 256m;
    sendfile on;
    #big file
    tcp_nopush on;
    #small file
    tcp_nodelay on;
    open_file_cache max=65535 inactive=60s;
    open_file_cache_valid 60s;
    open_file_cache_min_uses 1;
    open_file_cache_errors on;
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 9;
    gzip_buffers 16 8k;
    gzip_types image/jpeg image/gif image/x-icon image/png application/font-woff2 text/plain text/css application/x-font-ttf application/json application/x-javascript application/javascript text/javascript;
    gzip_vary on;
    gzip_proxied any;
    gzip_disable "MSIE [1-6]\.";
    gzip_http_version 1.1;
    add_header Access-Control-Allow-Origin *;
    server {
        listen *:8899 default_server;
        server_name resource.xyjm.store;
        location /ngx_status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            deny all;
          }
    }
    server {
        listen 8088;
        server_name resource.xyjm.store;
        keepalive_timeout 60;
        location / {
          proxy_pass http://127.0.0.1:8089;
          proxy_redirect off;
          proxy_set_header Host $host:$server_port;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
        }
        location ~* ^/(asset|business/common)/.+\.(jpg|jpeg|png|gif|css|js)$ {
          access_log off;
          add_header Cache-Control public;
          proxy_pass http://127.0.0.1:8089;
          proxy_redirect off;
          proxy_set_header Host $host:$server_port;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          add_header X-Cache-Status $upstream_cache_status;
          proxy_cache_key $host$uri$is_args$args;
          proxy_cache cache_one;
          proxy_cache_valid 200 7d;
          proxy_cache_valid 302 1h;
          proxy_cache_valid 301 1d;
          proxy_cache_valid any 1m;
          expires 10d;
        }
    }
    server {
        listen 8090 ssl;
        server_name resource.xyjm.store;
        keepalive_timeout 60;
        ssl_certificate /app/app/tengine/cert/resource.xyjm.store.pem;
        ssl_certificate_key /app/app/tengine/cert/resource.xyjm.store.key;
        ssl_session_timeout 5m;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
        location / {
          proxy_http_version 1.1;
          proxy_pass http://127.0.0.1:8089;
          proxy_redirect http:// https://;
          proxy_set_header Host $host:$server_port;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
        }
        location ~* ^/(assets|business/common)/.+\.(jpg|jpeg|png|gif|css|js)$ {
          proxy_http_version 1.1;
          access_log off;
          add_header Cache-Control public;
          proxy_pass http://127.0.0.1:8089;
          proxy_redirect http:// https://;
          proxy_set_header Host $host:$server_port;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_cache_key $host$uri$is_args$args;
          add_header X-Cache-Status $upstream_cache_status;
          proxy_cache cache_one;
          proxy_cache_valid 200 7d;
          proxy_cache_valid 302 1h;
          proxy_cache_valid 301 1d;
          proxy_cache_valid any 1m;
          expires 30d;
        }
    }
    server {
        listen 8091 ssl;
        server_name resource.xyjm.store;
        root  /app/app/tengine/html;
        index index.html index.htm;
        ssl_certificate /app/app/tengine/cert/resource.xyjm.store.pem;
        ssl_certificate_key /app/app/tengine/cert/resource.xyjm.store.key;
        ssl_session_timeout 5m;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
        ssl_prefer_server_ciphers on;
        location / {
            index index.html index.htm;
            proxy_redirect http:// https://;
            proxy_set_header Host $host:$server_port;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
         }
        location /service  {
            proxy_redirect http:// https://;
            proxy_set_header Host $host:$server_port;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://127.0.0.1:8089;
        }
        location /security {
            proxy_redirect http:// https://;
            proxy_set_header Host $host:$server_port;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://127.0.0.1:8089;
        }
    }
}