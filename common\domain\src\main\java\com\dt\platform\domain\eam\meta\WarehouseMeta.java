package com.dt.platform.domain.eam.meta;

import com.dt.platform.domain.eam.Warehouse;
import com.dt.platform.domain.eam.WarehousePosition;
import com.github.foxnic.api.bean.BeanProperty;

import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;



/**
 * <AUTHOR> , <EMAIL>
 * @since 2025-04-14 11:02:20
 * @sign 5962090A86AE5D5CF292EAD2439D15F4
 * 此文件由工具自动生成，请勿修改。若表结构或配置发生变动，请使用工具重新生成。
*/

public class WarehouseMeta {
	
	/**
	 * 编号 , 类型: java.lang.String
	 */
	public static final String CODE="code";
	
	/**
	 * 编号 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.Warehouse,java.lang.String> CODE_PROP = new BeanProperty(com.dt.platform.domain.eam.Warehouse.class ,CODE, java.lang.String.class, "编号", "编号", java.lang.String.class, null);
	
	/**
	 * 创建人ID , 类型: java.lang.String
	 */
	public static final String CREATE_BY="createBy";
	
	/**
	 * 创建人ID , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.Warehouse,java.lang.String> CREATE_BY_PROP = new BeanProperty(com.dt.platform.domain.eam.Warehouse.class ,CREATE_BY, java.lang.String.class, "创建人ID", "创建人ID", java.lang.String.class, null);
	
	/**
	 * 创建时间 , 类型: java.util.Date
	 */
	public static final String CREATE_TIME="createTime";
	
	/**
	 * 创建时间 , 类型: java.util.Date
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.Warehouse,java.util.Date> CREATE_TIME_PROP = new BeanProperty(com.dt.platform.domain.eam.Warehouse.class ,CREATE_TIME, java.util.Date.class, "创建时间", "创建时间", java.util.Date.class, null);
	
	/**
	 * 删除人ID , 类型: java.lang.String
	 */
	public static final String DELETE_BY="deleteBy";
	
	/**
	 * 删除人ID , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.Warehouse,java.lang.String> DELETE_BY_PROP = new BeanProperty(com.dt.platform.domain.eam.Warehouse.class ,DELETE_BY, java.lang.String.class, "删除人ID", "删除人ID", java.lang.String.class, null);
	
	/**
	 * 删除时间 , 类型: java.util.Date
	 */
	public static final String DELETE_TIME="deleteTime";
	
	/**
	 * 删除时间 , 类型: java.util.Date
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.Warehouse,java.util.Date> DELETE_TIME_PROP = new BeanProperty(com.dt.platform.domain.eam.Warehouse.class ,DELETE_TIME, java.util.Date.class, "删除时间", "删除时间", java.util.Date.class, null);
	
	/**
	 * 是否已删除 , 类型: java.lang.Integer
	 */
	public static final String DELETED="deleted";
	
	/**
	 * 是否已删除 , 类型: java.lang.Integer
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.Warehouse,java.lang.Integer> DELETED_PROP = new BeanProperty(com.dt.platform.domain.eam.Warehouse.class ,DELETED, java.lang.Integer.class, "是否已删除", "是否已删除", java.lang.Integer.class, null);
	
	/**
	 * 主键 , 类型: java.lang.String
	 */
	public static final String ID="id";
	
	/**
	 * 主键 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.Warehouse,java.lang.String> ID_PROP = new BeanProperty(com.dt.platform.domain.eam.Warehouse.class ,ID, java.lang.String.class, "主键", "主键", java.lang.String.class, null);
	
	/**
	 * 纬度 , 类型: java.math.BigDecimal
	 */
	public static final String LATITUDE="latitude";
	
	/**
	 * 纬度 , 类型: java.math.BigDecimal
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.Warehouse,java.math.BigDecimal> LATITUDE_PROP = new BeanProperty(com.dt.platform.domain.eam.Warehouse.class ,LATITUDE, java.math.BigDecimal.class, "纬度", "纬度", java.math.BigDecimal.class, null);
	
	/**
	 * 经度 , 类型: java.math.BigDecimal
	 */
	public static final String LONGITUDE="longitude";
	
	/**
	 * 经度 , 类型: java.math.BigDecimal
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.Warehouse,java.math.BigDecimal> LONGITUDE_PROP = new BeanProperty(com.dt.platform.domain.eam.Warehouse.class ,LONGITUDE, java.math.BigDecimal.class, "经度", "经度", java.math.BigDecimal.class, null);
	
	/**
	 * 管理人 , 类型: java.lang.String
	 */
	public static final String MANAGER_ID="managerId";
	
	/**
	 * 管理人 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.Warehouse,java.lang.String> MANAGER_ID_PROP = new BeanProperty(com.dt.platform.domain.eam.Warehouse.class ,MANAGER_ID, java.lang.String.class, "管理人", "管理人", java.lang.String.class, null);
	
	/**
	 * 地图位置 , 类型: java.lang.String
	 */
	public static final String MAP_ADDRESS="mapAddress";
	
	/**
	 * 地图位置 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.Warehouse,java.lang.String> MAP_ADDRESS_PROP = new BeanProperty(com.dt.platform.domain.eam.Warehouse.class ,MAP_ADDRESS, java.lang.String.class, "地图位置", "地图位置", java.lang.String.class, null);
	
	/**
	 * 状态 , 类型: java.lang.String
	 */
	public static final String STATUS="status";
	
	/**
	 * 状态 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.Warehouse,java.lang.String> STATUS_PROP = new BeanProperty(com.dt.platform.domain.eam.Warehouse.class ,STATUS, java.lang.String.class, "状态", "状态", java.lang.String.class, null);
	
	/**
	 * 租户 , 类型: java.lang.String
	 */
	public static final String TENANT_ID="tenantId";
	
	/**
	 * 租户 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.Warehouse,java.lang.String> TENANT_ID_PROP = new BeanProperty(com.dt.platform.domain.eam.Warehouse.class ,TENANT_ID, java.lang.String.class, "租户", "租户", java.lang.String.class, null);
	
	/**
	 * 修改人ID , 类型: java.lang.String
	 */
	public static final String UPDATE_BY="updateBy";
	
	/**
	 * 修改人ID , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.Warehouse,java.lang.String> UPDATE_BY_PROP = new BeanProperty(com.dt.platform.domain.eam.Warehouse.class ,UPDATE_BY, java.lang.String.class, "修改人ID", "修改人ID", java.lang.String.class, null);
	
	/**
	 * 修改时间 , 类型: java.util.Date
	 */
	public static final String UPDATE_TIME="updateTime";
	
	/**
	 * 修改时间 , 类型: java.util.Date
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.Warehouse,java.util.Date> UPDATE_TIME_PROP = new BeanProperty(com.dt.platform.domain.eam.Warehouse.class ,UPDATE_TIME, java.util.Date.class, "修改时间", "修改时间", java.util.Date.class, null);
	
	/**
	 * version , 类型: java.lang.Integer
	 */
	public static final String VERSION="version";
	
	/**
	 * version , 类型: java.lang.Integer
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.Warehouse,java.lang.Integer> VERSION_PROP = new BeanProperty(com.dt.platform.domain.eam.Warehouse.class ,VERSION, java.lang.Integer.class, "version", "version", java.lang.Integer.class, null);
	
	/**
	 * 名称 , 类型: java.lang.String
	 */
	public static final String WAREHOUSE_NAME="warehouseName";
	
	/**
	 * 名称 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.Warehouse,java.lang.String> WAREHOUSE_NAME_PROP = new BeanProperty(com.dt.platform.domain.eam.Warehouse.class ,WAREHOUSE_NAME, java.lang.String.class, "名称", "名称", java.lang.String.class, null);
	
	/**
	 * 备注 , 类型: java.lang.String
	 */
	public static final String WAREHOUSE_NOTES="warehouseNotes";
	
	/**
	 * 备注 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.Warehouse,java.lang.String> WAREHOUSE_NOTES_PROP = new BeanProperty(com.dt.platform.domain.eam.Warehouse.class ,WAREHOUSE_NOTES, java.lang.String.class, "备注", "备注", java.lang.String.class, null);
	
	/**
	 * warehousePositionList , 集合类型: LIST , 类型: com.dt.platform.domain.eam.WarehousePosition
	 */
	public static final String WAREHOUSE_POSITION_LIST="warehousePositionList";
	
	/**
	 * warehousePositionList , 集合类型: LIST , 类型: com.dt.platform.domain.eam.WarehousePosition
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.Warehouse,com.dt.platform.domain.eam.WarehousePosition> WAREHOUSE_POSITION_LIST_PROP = new BeanProperty(com.dt.platform.domain.eam.Warehouse.class ,WAREHOUSE_POSITION_LIST, java.util.List.class, "warehousePositionList", "warehousePositionList", com.dt.platform.domain.eam.WarehousePosition.class, null);
	
	/**
	 * 经纬度 , 类型: java.lang.String
	 */
	public static final String LATITUDE_LONGITUDE_STR="latitudeLongitudeStr";
	
	/**
	 * 经纬度 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.Warehouse,java.lang.String> LATITUDE_LONGITUDE_STR_PROP = new BeanProperty(com.dt.platform.domain.eam.Warehouse.class ,LATITUDE_LONGITUDE_STR, java.lang.String.class, "经纬度", "经纬度", java.lang.String.class, null);
	
	/**
	 * 全部属性清单
	 */
	public static final String[] $PROPS={ CODE , CREATE_BY , CREATE_TIME , DELETE_BY , DELETE_TIME , DELETED , ID , LATITUDE , LONGITUDE , MANAGER_ID , MAP_ADDRESS , STATUS , TENANT_ID , UPDATE_BY , UPDATE_TIME , VERSION , WAREHOUSE_NAME , WAREHOUSE_NOTES , WAREHOUSE_POSITION_LIST , LATITUDE_LONGITUDE_STR };
	
	/**
	 * 代理类
	 */
	public static class $$proxy$$ extends com.dt.platform.domain.eam.Warehouse {

		private static final long serialVersionUID = 1L;

		
		/**
		 * 设置 编号
		 * @param code 编号
		 * @return 当前对象
		*/
		public Warehouse setCode(String code) {
			super.change(CODE,super.getCode(),code);
			super.setCode(code);
			return this;
		}
		
		/**
		 * 设置 创建人ID
		 * @param createBy 创建人ID
		 * @return 当前对象
		*/
		public Warehouse setCreateBy(String createBy) {
			super.change(CREATE_BY,super.getCreateBy(),createBy);
			super.setCreateBy(createBy);
			return this;
		}
		
		/**
		 * 设置 创建时间
		 * @param createTime 创建时间
		 * @return 当前对象
		*/
		public Warehouse setCreateTime(Date createTime) {
			super.change(CREATE_TIME,super.getCreateTime(),createTime);
			super.setCreateTime(createTime);
			return this;
		}
		
		/**
		 * 设置 删除人ID
		 * @param deleteBy 删除人ID
		 * @return 当前对象
		*/
		public Warehouse setDeleteBy(String deleteBy) {
			super.change(DELETE_BY,super.getDeleteBy(),deleteBy);
			super.setDeleteBy(deleteBy);
			return this;
		}
		
		/**
		 * 设置 删除时间
		 * @param deleteTime 删除时间
		 * @return 当前对象
		*/
		public Warehouse setDeleteTime(Date deleteTime) {
			super.change(DELETE_TIME,super.getDeleteTime(),deleteTime);
			super.setDeleteTime(deleteTime);
			return this;
		}
		
		/**
		 * 设置 是否已删除
		 * @param deleted 是否已删除
		 * @return 当前对象
		*/
		public Warehouse setDeleted(Integer deleted) {
			super.change(DELETED,super.getDeleted(),deleted);
			super.setDeleted(deleted);
			return this;
		}
		
		/**
		 * 设置 主键
		 * @param id 主键
		 * @return 当前对象
		*/
		public Warehouse setId(String id) {
			super.change(ID,super.getId(),id);
			super.setId(id);
			return this;
		}
		
		/**
		 * 设置 纬度
		 * @param latitude 纬度
		 * @return 当前对象
		*/
		public Warehouse setLatitude(BigDecimal latitude) {
			super.change(LATITUDE,super.getLatitude(),latitude);
			super.setLatitude(latitude);
			return this;
		}
		
		/**
		 * 设置 经度
		 * @param longitude 经度
		 * @return 当前对象
		*/
		public Warehouse setLongitude(BigDecimal longitude) {
			super.change(LONGITUDE,super.getLongitude(),longitude);
			super.setLongitude(longitude);
			return this;
		}
		
		/**
		 * 设置 管理人
		 * @param managerId 管理人
		 * @return 当前对象
		*/
		public Warehouse setManagerId(String managerId) {
			super.change(MANAGER_ID,super.getManagerId(),managerId);
			super.setManagerId(managerId);
			return this;
		}
		
		/**
		 * 设置 地图位置
		 * @param mapAddress 地图位置
		 * @return 当前对象
		*/
		public Warehouse setMapAddress(String mapAddress) {
			super.change(MAP_ADDRESS,super.getMapAddress(),mapAddress);
			super.setMapAddress(mapAddress);
			return this;
		}
		
		/**
		 * 设置 状态
		 * @param status 状态
		 * @return 当前对象
		*/
		public Warehouse setStatus(String status) {
			super.change(STATUS,super.getStatus(),status);
			super.setStatus(status);
			return this;
		}
		
		/**
		 * 设置 租户
		 * @param tenantId 租户
		 * @return 当前对象
		*/
		public Warehouse setTenantId(String tenantId) {
			super.change(TENANT_ID,super.getTenantId(),tenantId);
			super.setTenantId(tenantId);
			return this;
		}
		
		/**
		 * 设置 修改人ID
		 * @param updateBy 修改人ID
		 * @return 当前对象
		*/
		public Warehouse setUpdateBy(String updateBy) {
			super.change(UPDATE_BY,super.getUpdateBy(),updateBy);
			super.setUpdateBy(updateBy);
			return this;
		}
		
		/**
		 * 设置 修改时间
		 * @param updateTime 修改时间
		 * @return 当前对象
		*/
		public Warehouse setUpdateTime(Date updateTime) {
			super.change(UPDATE_TIME,super.getUpdateTime(),updateTime);
			super.setUpdateTime(updateTime);
			return this;
		}
		
		/**
		 * 设置 version
		 * @param version version
		 * @return 当前对象
		*/
		public Warehouse setVersion(Integer version) {
			super.change(VERSION,super.getVersion(),version);
			super.setVersion(version);
			return this;
		}
		
		/**
		 * 设置 名称
		 * @param warehouseName 名称
		 * @return 当前对象
		*/
		public Warehouse setWarehouseName(String warehouseName) {
			super.change(WAREHOUSE_NAME,super.getWarehouseName(),warehouseName);
			super.setWarehouseName(warehouseName);
			return this;
		}
		
		/**
		 * 设置 备注
		 * @param warehouseNotes 备注
		 * @return 当前对象
		*/
		public Warehouse setWarehouseNotes(String warehouseNotes) {
			super.change(WAREHOUSE_NOTES,super.getWarehouseNotes(),warehouseNotes);
			super.setWarehouseNotes(warehouseNotes);
			return this;
		}
		
		/**
		 * 设置 warehousePositionList
		 * @param warehousePositionList warehousePositionList
		 * @return 当前对象
		*/
		public Warehouse setWarehousePositionList(List<WarehousePosition> warehousePositionList) {
			super.change(WAREHOUSE_POSITION_LIST,super.getWarehousePositionList(),warehousePositionList);
			super.setWarehousePositionList(warehousePositionList);
			return this;
		}
		
		/**
		 * 设置 经纬度
		 * @param latitudeLongitudeStr 经纬度
		 * @return 当前对象
		*/
		public Warehouse setLatitudeLongitudeStr(String latitudeLongitudeStr) {
			super.change(LATITUDE_LONGITUDE_STR,super.getLatitudeLongitudeStr(),latitudeLongitudeStr);
			super.setLatitudeLongitudeStr(latitudeLongitudeStr);
			return this;
		}

		/**
		 * 克隆当前对象
		*/
		@Transient
		public Warehouse clone() {
			return duplicate(true);
		}

		/**
		 * 复制当前对象
		 * @param all 是否复制全部属性，当 false 时，仅复制来自数据表的属性
		*/
		@Transient
		public Warehouse duplicate(boolean all) {
			$$proxy$$ inst=new $$proxy$$();
			inst.setCode(this.getCode());
			inst.setLatitude(this.getLatitude());
			inst.setUpdateTime(this.getUpdateTime());
			inst.setManagerId(this.getManagerId());
			inst.setVersion(this.getVersion());
			inst.setWarehouseName(this.getWarehouseName());
			inst.setCreateBy(this.getCreateBy());
			inst.setDeleted(this.getDeleted());
			inst.setCreateTime(this.getCreateTime());
			inst.setDeleteTime(this.getDeleteTime());
			inst.setUpdateBy(this.getUpdateBy());
			inst.setTenantId(this.getTenantId());
			inst.setDeleteBy(this.getDeleteBy());
			inst.setId(this.getId());
			inst.setWarehouseNotes(this.getWarehouseNotes());
			inst.setLongitude(this.getLongitude());
			inst.setMapAddress(this.getMapAddress());
			inst.setStatus(this.getStatus());
			if(all) {
				inst.setLatitudeLongitudeStr(this.getLatitudeLongitudeStr());
				inst.setWarehousePositionList(this.getWarehousePositionList());
			}
			inst.clearModifies();
			return inst;
		}

	}
}