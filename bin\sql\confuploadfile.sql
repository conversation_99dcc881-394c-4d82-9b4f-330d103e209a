-- EAM
update sys_config set value=CAST(NOW() AS CHAR)  where id='system.releaseTime';
delete from sys_file where id like 'T001_eam_%';
delete from sys_file where id like 'T001_hr_%';
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_1','eam_asset_repair.docx','/tpl/T001/eam_asset_repair.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_2','eam_asset_borrow.docx','/tpl/T001/eam_asset_borrow.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_3','eam_asset_collection.docx','/tpl/T001/eam_asset_collection.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_4','eam_asset_collection_return.docx','/tpl/T001/eam_asset_collection_return.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_5','eam_asset_allocate.docx','/tpl/T001/eam_asset_allocate.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_6','eam_asset_label.docx','/tpl/T001/eam_asset_label.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_7','eam_asset_card.docx','/tpl/T001/eam_asset_card.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_8','eam_asset_tranfer.docx','/tpl/T001/eam_asset_tranfer.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_9','eam_download_asset.xlsx','/tpl/T001/eam_download_asset.xlsx',10000,'application/octet-stream','xlsx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_10','eam_download_scrap.docx','/tpl/T001/eam_asset_scrap.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_11','eam_asset_register.docx','/tpl/T001/eam_asset_register.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_12','eam_asset_stock_goods_in.docx','/tpl/T001/eam_asset_stock_goods_in.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_13','eam_asset_stock_goods_out.docx','/tpl/T001/eam_asset_stock_goods_out.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_14','eam_asset_stock_goods_tranfer.docx','/tpl/T001/eam_asset_stock_goods_tranfer.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_15','eam_asset_stock_goods_adjust.docx','/tpl/T001/eam_asset_stock_goods_adjust.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_16','eam_asset_consumables_goods_in.docx','/tpl/T001/eam_asset_consumables_goods_in.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_17','eam_asset_consumables_goods_out.docx','/tpl/T001/eam_asset_consumables_goods_out.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_18','eam_asset_consumables_goods_tranfer.docx','/tpl/T001/eam_asset_consumables_goods_tranfer.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_19','eam_asset_consumables_goods_adjust.docx','/tpl/T001/eam_asset_consumables_goods_adjust.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_20','eam_asset_part_goods_in.docx','/tpl/T001/eam_asset_part_goods_in.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_21','eam_asset_part_goods_out.docx','/tpl/T001/eam_asset_part_goods_out.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_22','eam_asset_part_goods_tranfer.docx','/tpl/T001/eam_asset_part_goods_tranfer.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_23','eam_asset_part_goods_adjust.docx','/tpl/T001/eam_asset_part_goods_adjust.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_24','eam_batch_upload_asset.xls','/tpl/T001/eam_batch_upload_asset.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_25','eam_asset_storage.docx','/tpl/T001/eam_asset_storage.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_26','eam_asset_repair_order.docx','/tpl/T001/eam_asset_repair_order.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_27','eam_inventory.docx','/tpl/T001/eam_inventory.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_28','eam_inventory_asset.xls','/tpl/T001/eam_inventory_asset.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_29','eam_asset_borrow_return.docx','/tpl/T001/eam_asset_borrow_return.docx',10000,'application/octet-stream','docx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_30','eam_asset_download_depreciation_report.xls','/tpl/T001/eam_asset_download_depreciation_report.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_31','eam_asset_insepect_detail.xls','/tpl/T001/eam_asset_insepect_detail.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_32','eam_asset_insepect_point.xls','/tpl/T001/eam_asset_insepect_point.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_33','eam_standard_goods.xls','/tpl/T001/eam_standard_goods.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_34','eam_goods_import.xls','/tpl/T001/eam_goods_import.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_35','eam_goods_export.xls','/tpl/T001/eam_goods_export.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_36','eam_goods_adjust.xls','/tpl/T001/eam_goods_adjust.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_37','eam_goods_part_import.xls','/tpl/T001/eam_goods_part_import.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_38','eam_goods_part_export.xls','/tpl/T001/eam_goods_part_export.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_39','eam_goods_part_adjust.xls','/tpl/T001/eam_goods_part_adjust.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_40','eam_category_pcm.xlsx','/tpl/T001/eam_category_pcm.xlsx',10000,'application/octet-stream','xlsx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_41','eam_category_ext.xlsx','/tpl/T001/eam_category_ext.xlsx',10000,'application/octet-stream','xlsx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_42','eam_inspect_check_item.xlsx','/tpl/T001/eam_inspect_check_item.xlsx',10000,'application/octet-stream','xlsx');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_hr_1','hr_person_salary_detail.xls','/tpl/T001/hr_person_salary_detail.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_hr_2','hr_salary_commission.xls','/tpl/T001/hr_salary_commission.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_hr_3','hr_salary_time.xls','/tpl/T001/hr_salary_time.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_hr_4','hr_salary_unit.xls','/tpl/T001/hr_salary_unit.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_hr_5','hr_person_personal_tax.xls','/tpl/T001/hr_person_personal_tax.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_hr_6','hr_person_welfaer.xls','/tpl/T001/hr_person_welfaer.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_hr_7','hr_person_attendance_rcd.xls','/tpl/T001/hr_person_attendance_rcd.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_hr_8','hr_person.xls','/tpl/T001/hr_person.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_hr_9','hr_person_busi_insure.xls','/tpl/T001/hr_person_busi_insure.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_hr_10','hr_person_welfaer_s.xls','/tpl/T001/hr_person_welfaer_s.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_hr_11','hr_person_welfaer_g.xls','/tpl/T001/hr_person_welfaer_g.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_hr_12','hr_person_kh_score.xls','/tpl/T001/hr_person_kh_score.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_hr_13','hr_person_salary_base.xls','/tpl/T001/hr_person_salary_base.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_hr_14','hr_person_attendance_record.xls','/tpl/T001/hr_person_attendance_record.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_hr_15','hr_attendance_overtime.xls','/tpl/T001/hr_attendance_overtime.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_hr_16','hr_attendance_official_busi.xls','/tpl/T001/hr_attendance_official_busi.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_hr_17','hr_attendance_holiday.xls','/tpl/T001/hr_attendance_holiday.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_hr_18','hr_paper_question.xls','/tpl/T001/hr_paper_question.xls',10000,'application/octet-stream','xls');
delete from sys_tpl_file where tenant_id='T001' and type in ('eam_bill_docx','eam_asset_excel','hr_excel');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_1','eam_bill_docx','资产报修','eam_download_asset_repair_bill','T001_eam_1','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_2','eam_bill_docx','资产借用','eam_download_asset_borrow_bill','T001_eam_2','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_3','eam_bill_docx','资产领用','eam_download_asset_collection_bill','T001_eam_3','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_4','eam_bill_docx','资产领用退库','eam_download_asset_collection_return_bill','T001_eam_4','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_5','eam_bill_docx','资产调拨','eam_download_asset_allocate_bill','T001_eam_5','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_6','eam_bill_docx','资产标签','eam_download_asset_label','T001_eam_6','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_7','eam_bill_docx','资产编卡片','eam_download_asset_card','T001_eam_7','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_8','eam_bill_docx','资产转移','eam_download_asset_tranfer_bill','T001_eam_8','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_9','eam_asset_excel','资产数据下载','eam_download_asset','T001_eam_9','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_10','eam_bill_docx','资产报废','eam_download_asset_scrap_bill','T001_eam_10','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_11','eam_bill_docx','资产登记','eam_download_asset_register_bill','T001_eam_11','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_12','eam_bill_docx','库存物品入库','eam_download_asset_stock_goods_in_bill','T001_eam_12','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_13','eam_bill_docx','库存物品出库','eam_download_asset_stock_goods_out_bill','T001_eam_13','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_14','eam_bill_docx','库存物品转移','eam_download_asset_stock_goods_tranfer_bill','T001_eam_14','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_15','eam_bill_docx','库存物品调整','eam_download_asset_stock_goods_adjust_bill','T001_eam_15','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_16','eam_bill_docx','耗材物品入库','eam_download_asset_consumables_goods_in_bill','T001_eam_16','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_17','eam_bill_docx','耗材物品出库','eam_download_asset_consumables_goods_out_bill','T001_eam_17','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_18','eam_bill_docx','耗材物品转移','eam_download_asset_consumables_goods_tranfer_bill','T001_eam_18','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_19','eam_bill_docx','耗材物品调整','eam_download_asset_consumables_goods_adjust_bill','T001_eam_19','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_20','eam_bill_docx','备件物品入库','eam_download_asset_part_goods_in_bill','T001_eam_20','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_21','eam_bill_docx','备件物品出库','eam_download_asset_part_goods_out_bill','T001_eam_21','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_22','eam_bill_docx','备件物品转移','eam_download_asset_part_goods_tranfer_bill','T001_eam_22','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_23','eam_bill_docx','备件物品调整','eam_download_asset_part_goods_adjust_bill','T001_eam_23','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_24','eam_asset_excel','资产数据上传','eam_batch_upload_asset','T001_eam_24','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_25','eam_bill_docx','资产入库单据','eam_download_asset_storage_bill','T001_eam_25','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_26','eam_bill_docx','资产维修单据','eam_download_asset_repair_order_bill','T001_eam_26','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_27','eam_bill_docx','盘点单据','eam_download_asset_inventory_bill','T001_eam_27','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_28','eam_asset_excel','盘点资产','eam_download_asset_inventory_asset','T001_eam_28','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_29','eam_bill_docx','资产归还','eam_download_asset_borrow_return_bill','T001_eam_29','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_30','eam_asset_excel','资产折旧','eam_asset_download_depreciation_report','T001_eam_30','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_31','eam_asset_excel','设备巡检','eam_asset_insepect_detail','T001_eam_31','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_32','eam_asset_excel','巡检点位','eam_asset_insepect_point','T001_eam_32','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_33','eam_asset_excel','物品档案','eam_standard_goods','T001_eam_33','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_34','eam_asset_excel','物品导入','eam_goods_import','T001_eam_34','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_35','eam_asset_excel','物品导出','eam_goods_export','T001_eam_35','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_36','eam_asset_excel','物品调整','eam_goods_adjust','T001_eam_36','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_37','eam_asset_excel','备件导入','eam_goods_part_import','T001_eam_37','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_38','eam_asset_excel','备件导出','eam_goods_part_export','T001_eam_38','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_39','eam_asset_excel','备件调整','eam_goods_part_adjust','T001_eam_39','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_40','eam_asset_excel','资产分类','eam_category_pcm','T001_eam_40','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_41','eam_asset_excel','分类扩展','eam_category_ext','T001_eam_41','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_eam_42','eam_asset_excel','巡检检查项','eam_inspect_check_item','T001_eam_42','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_hr_1','hr_excel','人员薪酬','hr_person_salary_detail','T001_hr_1','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_hr_2','hr_excel','提成','hr_salary_commission','T001_hr_2','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_hr_3','hr_excel','计时','hr_salary_time','T001_hr_3','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_hr_4','hr_excel','计件','hr_salary_unit','T001_hr_4','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_hr_5','hr_excel','专项抵扣','hr_person_personal_tax','T001_hr_5','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_hr_6','hr_excel','福利信息','hr_person_welfaer','T001_hr_6','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_hr_7','hr_excel','考勤记录','hr_person_attendance_rcd','T001_hr_7','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_hr_8','hr_excel','个人信息','hr_person','T001_hr_8','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_hr_9','hr_excel','商业保险','hr_person_busi_insure','T001_hr_9','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_hr_10','hr_excel','社保','hr_person_welfaer_s','T001_hr_10','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_hr_11','hr_excel','公积金','hr_person_welfaer_g','T001_hr_11','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_hr_12','hr_excel','考核评分','hr_person_kh_score','T001_hr_12','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_hr_13','hr_excel','员工薪酬','hr_person_salary_base','T001_hr_13','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_hr_14','hr_excel','考勤记录','hr_person_attendance_record','T001_hr_14','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_hr_15','hr_excel','加班管理','hr_attendance_overtime','T001_hr_15','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_hr_16','hr_excel','出差管理','hr_attendance_official_busi','T001_hr_16','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_hr_17','hr_excel','休假管理','hr_attendance_holiday','T001_hr_17','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_hr_18','hr_excel','题库数据','hr_paper_question','T001_hr_18','T001');
-- OPS
delete from sys_file where id like 'T001_ops_%';
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_ops_1','ops_download_host.xls','/tpl/T001/ops_download_host.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_ops_2','ops_download_database_inst.xls','/tpl/T001/ops_download_database_inst.xls',10000,'application/octet-stream','xls');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_ops_3','ops_download_information_system.xls','/tpl/T001/ops_download_information_system.xls',10000,'application/octet-stream','xls');
delete from sys_tpl_file where tenant_id='T001' and code in ('ops_download_host','ops_download_database_inst','ops_download_information_system');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_ops_1','eam_asset_excel','主机数据下载','ops_download_host','T001_ops_1','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_ops_2','eam_asset_excel','数据库数据下载','ops_download_database_inst','T001_ops_2','T001');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_ops_3','eam_asset_excel','信息系统数据下载','ops_download_information_system','T001_ops_3','T001');
-- VEHICLE
delete from sys_file where id like 'T001_vehicle_%';
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_vehicle_1','vehicle_info_download.xls','/tpl/T001/vehicle_info_download.xls',10000,'application/octet-stream','xls');
delete from sys_tpl_file where tenant_id='T001' and code in ('vehicle_info_download');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_vehicle_1','eam_asset_excel','车辆数据下载下载','vehicle_info_download','T001_vehicle_1','T001');
-- CONT
delete from sys_file where id like 'T001_cont_%';
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_cont_1','cont_download_info.xls','/tpl/T001/cont_download_info.xls',10000,'application/octet-stream','xls');
delete from sys_tpl_file where tenant_id='T001' and code in ('cont_download_info');
insert into sys_tpl_file(id,type,name,code,file_id,tenant_id)values('T001_cont_1','cont','合同下载','cont_download_info','T001_cont_1','T001');
-- Mobile
delete from app_software_info where id in ('T001_eam_mobile_a1','T001_eam_mobile_a2');
delete from sys_file where id='T001_eam_mobile_a1';
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_mobile_a1','app.apk','/software/app.apk',10000,'application/octet-stream','apk');
insert into `app_software_info` (`id`, `code`, `group_id`, `name`, `status`, `type`, `software_version`, `picture_id`, `file_id`,`tenant_id`) VALUES ('T001_eam_mobile_a1', 'eam_mobile_android', 'eam_mobile', '安卓移动端', 'enable', 'android', '2.4.0', '', 'T001_eam_mobile_a1', 'T001');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_eam_mobile_a2','h5.tar.gz','/software/h5.tar.gz',10000,'application/octet-stream','gz');
insert into `app_software_info` (`id`, `code`, `group_id`, `name`, `status`, `type`, `software_version`, `picture_id`, `file_id`,`tenant_id`) VALUES ('T001_eam_mobile_a2', 'eam_mobile_h5', 'eam_mobile', 'H5版本介质', 'enable', 'h5', '2.4.0', '', 'T001_eam_mobile_a2', 'T001');
-- oa
delete from sys_file where id in ('T001_image_head','T001_image_banner1','T001_image_banner2');
delete from sys_file where id in ('T001_image_project');
INSERT INTO sys_file(`id`, `file_name`, `location`, `size`, `media_type`, `file_type`, `download_url`, `latest_visit_time`, `downloads`, `create_by`, `create_time`, `update_by`, `update_time`, `deleted`, `delete_by`, `delete_time`, `version`) VALUES ('T001_image_head', 'head.png', '/image/T001/head.png', 66222, 'image/png', 'png', NULL, '2023-05-12 13:30:32', 64, '110588348101165911', '2023-05-12 07:32:51', NULL, NULL, 0, NULL, NULL, 1);
INSERT INTO sys_file(`id`, `file_name`, `location`, `size`, `media_type`, `file_type`, `download_url`, `latest_visit_time`, `downloads`, `create_by`, `create_time`, `update_by`, `update_time`, `deleted`, `delete_by`, `delete_time`, `version`) VALUES ('T001_image_banner1', 'banner1.png', '/image/T001/portal_banner1.png', 66222, 'image/png', 'png', NULL, '2023-05-12 13:30:32', 64, '110588348101165911', '2023-05-12 07:32:51', NULL, NULL, 0, NULL, NULL, 1);
INSERT INTO sys_file(`id`, `file_name`, `location`, `size`, `media_type`, `file_type`, `download_url`, `latest_visit_time`, `downloads`, `create_by`, `create_time`, `update_by`, `update_time`, `deleted`, `delete_by`, `delete_time`, `version`) VALUES ('T001_image_banner2', 'banner2.png', '/image/T001/portal_banner2.png', 66222, 'image/png', 'png', NULL, '2023-05-12 13:30:32', 64, '110588348101165911', '2023-05-12 07:32:51', NULL, NULL, 0, NULL, NULL, 1);
INSERT INTO sys_file(`id`, `file_name`, `location`, `size`, `media_type`, `file_type`, `download_url`, `latest_visit_time`, `downloads`, `create_by`, `create_time`, `update_by`, `update_time`, `deleted`, `delete_by`, `delete_time`, `version`) VALUES ('T001_image_project', 'project.png', '/image/T001/project.png', 66222, 'image/png', 'png', NULL, '2023-05-12 13:30:32', 64, '110588348101165911', '2023-05-12 07:32:51', NULL, NULL, 0, NULL, NULL, 1);
-- common
delete from sys_file where id like 'T001_common%';
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_common_1','common_user_import.xls','/tpl/T001/common_user_import.xls',10000,'application/octet-stream','xls');

delete from sys_file where id in ('T001_docs_1','T001_docs_2','T001_docs_3','T001_docs_4','T001_docs_5','T001_docs_6');
INSERT INTO `sys_file` (`id`, `file_name`, `location`, `size`, `media_type`, `file_type`, `download_url`, `latest_visit_time`, `downloads`, `create_by`, `create_time`, `update_by`, `update_time`, `deleted`, `delete_by`, `delete_time`, `version`)
VALUES
('T001_docs_1', 'system_update_last.doc', '/docs/T001/system_update_last.doc', 28160, 'application/msword', 'doc', NULL, '2023-07-19 06:52:59', 4, '110588348101165911', '2023-05-18 10:33:57', NULL, NULL, 0, NULL, NULL, 1),
('T001_docs_2', 'user_manual_last.pdf', '/docs/T001/user_manual_last.pdf', 14885075, 'application/pdf', 'pdf', NULL, '2023-05-28 23:03:40', 3, '110588348101165911', '2023-05-18 10:20:06', NULL, NULL, 0, NULL, NULL, 1),
('T001_docs_3', 'system_license_last.doc', '/docs/T001/system_license_last.doc', 15160, 'application/msword', 'doc', NULL, '2023-07-19 06:52:59', 4, '110588348101165911', '2023-05-18 10:33:57', NULL, NULL, 0, NULL, NULL, 1),
('T001_docs_4', 'system_view_oper.doc', '/docs/T001/system_view_oper.doc', 15160, 'application/msword', 'doc', NULL, '2023-07-19 06:52:59', 4, '110588348101165911', '2023-05-18 10:33:57', NULL, NULL, 0, NULL, NULL, 1),
('T001_docs_5', 'system_mobile_oper.doc', '/docs/T001/system_mobile_oper.doc', 15160, 'application/msword', 'doc', NULL, '2023-07-19 06:52:59', 4, '110588348101165911', '2023-05-18 10:33:57', NULL, NULL, 0, NULL, NULL, 1),
('T001_docs_6', 'system_dev_oper.doc', '/docs/T001/system_dev_oper.doc', 15160, 'application/msword', 'doc', NULL, '2023-07-19 06:52:59', 4, '110588348101165911', '2023-05-18 10:33:57', NULL, NULL, 0, NULL, NULL, 1);

-- Ops Auto Task File
-- File
delete from sys_file where id like 'T001_ops_f_%';
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_ops_f_3','mysql-5.7.32-linux-glibc2.12-x86_64.tar.gz','/media/T001/mysql-5.7.32-linux-glibc2.12-x86_64.tar.gz',10000,'application/x-tar','gz');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_ops_f_4','redis-5.0.14.tar.gz','/media/T001/redis-5.0.14.tar.gz',10000,'application/x-tar','gz');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_ops_f_5','nginx-1.24.0.tar.gz','/media/T001/nginx-1.24.0.tar.gz',10000,'application/x-tar','gz');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_ops_f_6','apache-tomcat-9.0.80.tar.gz','/media/T001/apache-tomcat-9.0.80.tar.gz',10000,'application/x-tar','gz');
-- insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_ops_f_1','db2v11_db2ese_c.lic','/media/T001/db2v11_db2ese_c.lic',10000,'application/octet-stream','lic');
-- insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_ops_f_2','db2v11_tar.gz','/media/T001/db2v11_tar.gz',10000,'application/x-tar','gz');
-- Action File
delete from ops_auto_action_file where id like 'T001_ops_f_%';
insert into ops_auto_action_file(id,name,file_name,file_id,tenant_id)values('T001_ops_f_3','mysql-5.7.32-linux-glibc2.12-x86_64.tar.gz','mysql.tar.gz','T001_ops_f_3','T001');
insert into ops_auto_action_file(id,name,file_name,file_id,tenant_id)values('T001_ops_f_4','redis-5.0.14.tar.gz','redis.tar.gz','T001_ops_f_4','T001');
insert into ops_auto_action_file(id,name,file_name,file_id,tenant_id)values('T001_ops_f_5','nginx-1.24.0.tar.gz','nginx.tar.gz','T001_ops_f_5','T001');
insert into ops_auto_action_file(id,name,file_name,file_id,tenant_id)values('T001_ops_f_6','apache-tomcat-9.0.80.tar.gz','tomcat.tar.gz','T001_ops_f_6','T001');
-- insert into ops_auto_action_file(id,name,file_name,file_id,tenant_id)values('T001_ops_f_1','db2v11_db2ese_c.lic','db2v11_db2ese_c.lic','T001_ops_f_1','T001');
-- insert into ops_auto_action_file(id,name,file_name,file_id,tenant_id)values('T001_ops_f_2','db2v11_tar.gz','db2v11_tar.gz','T001_ops_f_2','T001');
-- Script
delete from sys_file where id like 'T001_ops_s_%';
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_ops_s_1','deploy_tomcat.sh','/script/T001/deploy_tomcat.sh',10000,'text/x-sh','sh');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_ops_s_2','deploy_nginx.sh','/script/T001/deploy_nginx.sh',10000,'text/x-sh','sh');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_ops_s_3','deploy_mysql.sh','/script/T001/deploy_mysql.sh',10000,'text/x-sh','sh');
insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_ops_s_7','deploy_redis_single.sh','/script/T001/deploy_redis_single.sh',10000,'text/x-sh','sh');
-- insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_ops_s_4','deploy_db2.sh','/script/T001/deploy_db2.sh',10000,'text/x-sh','sh');
-- insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_ops_s_5','deploy_zabbix_agent.sh','/script/T001/deploy_zabbix_agent.sh',10000,'text/x-sh','sh');
-- insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_ops_s_6','deploy_was_single.sh','/script/T001/deploy_was_single.sh',10000,'text/x-sh','sh');
-- insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_ops_s_8','backup_db2.sh','/script/T001/backup_db2.sh',10000,'text/x-sh','sh');
-- insert into sys_file(id,file_name,location,size,media_type,file_type)values('T001_ops_s_9','backup_mysql.sh','/script/T001/backup_mysql.sh',10000,'text/x-sh','sh');
-- Action File
delete from ops_auto_action_script where id like 'T001_ops_s_%';
insert into ops_auto_action_script(id,name,file_name,file_id,tenant_id)values('T001_ops_s_1','deploy_tomcat.sh','deploy_tomcat.sh','T001_ops_s_1','T001');
insert into ops_auto_action_script(id,name,file_name,file_id,tenant_id)values('T001_ops_s_2','deploy_nginx.sh','deploy_nginx.sh','T001_ops_s_2','T001');
insert into ops_auto_action_script(id,name,file_name,file_id,tenant_id)values('T001_ops_s_3','deploy_mysql.sh','deploy_mysql.sh','T001_ops_s_3','T001');
insert into ops_auto_action_script(id,name,file_name,file_id,tenant_id)values('T001_ops_s_7','deploy_redis_single.sh','deploy_redis_single.sh','T001_ops_s_7','T001');
-- insert into ops_auto_action_script(id,name,file_name,file_id,tenant_id)values('T001_ops_s_4','deploy_db2.sh','deploy_db2.sh','T001_ops_s_4','T001');
-- insert into ops_auto_action_script(id,name,file_name,file_id,tenant_id)values('T001_ops_s_5','deploy_zabbix_agent.sh','deploy_zabbix_agent.sh','T001_ops_s_5','T001');
-- insert into ops_auto_action_script(id,name,file_name,file_id,tenant_id)values('T001_ops_s_6','deploy_was_single.sh','deploy_was_single.sh','T001_ops_s_6','T001');
-- insert into ops_auto_action_script(id,name,file_name,file_id,tenant_id)values('T001_ops_s_8','backup_db2.sh','backup_db2.sh','T001_ops_s_8','T001');
-- insert into ops_auto_action_script(id,name,file_name,file_id,tenant_id)values('T001_ops_s_9','backup_mysql.sh','backup_mysql.sh','T001_ops_s_9','T001');
delete from ops_auto_task where id like 'T001%';
INSERT INTO `ops_auto_task` (`id`, `owner_id`, `name`, `status`, `run_status`, `group_id`, `batch_id`, `action_id`, `conf_content`, `notes`, `selected_code`, `update_by`, `update_time`, `deleted`, `delete_by`, `delete_time`, `version`, `tenant_id`, `create_by`, `create_time`)
VALUES
('T001_mysql', 'task', 'Linux部署mysql', 'enable', NULL, NULL, '', 'T001_mysql', '[]', '', '1693566914000', NULL, NULL, 0, NULL, NULL, 1, 'T001', '110588348101165911', '2023-09-01 19:15:30'),
('T001_nginx', 'task', 'Linux部署nginx', 'enable', 'finish', NULL, '', 'T001_nginx', '[]', '', '1693620001000', NULL, '2023-09-02 10:12:49', 0, NULL, NULL, 8, 'T001', '110588348101165911', '2023-09-01 19:15:53'),
('T001_redis', 'task', 'Linux部署redis', 'enable', 'finish', NULL, '', 'T001_redis', '[]', '', '1693619841000', NULL, '2023-09-02 09:59:48', 0, NULL, NULL, 9, 'T001', '110588348101165911', '2023-09-01 19:15:13'),
('T001_test', 'task', 'Linux自动运行测试', 'enable', 'finish', NULL, '', 'T001_test', '', '', '1693567086000', '110588348101165911', '2023-09-01 19:18:10', 0, NULL, NULL, 66, 'T001', NULL, NULL),
('T001_tomcat', 'task', 'Linux部署tomcat', 'enable', 'finish', NULL, '', 'T001_tomcat', '[]', '', '1693619283000', NULL, '2023-09-02 10:03:31', 0, NULL, NULL, 12, 'T001', '110588348101165911', '2023-09-01 19:12:56');
delete from ops_auto_action where id like 'T001%';
INSERT INTO `ops_auto_action` (`id`, `name`, `status`, `type`, `tpl_version`, `support`, `info`, `file_status`, `node_number_type`, `conf_content`, `example_conf_content`, `execute_content`, `execute_tool`, `notes`, `create_by`, `create_time`, `update_by`, `update_time`, `deleted`, `delete_by`, `delete_time`, `version`, `tenant_id`)
VALUES
('T001_base', 'Linux安全基线', 'enable', 'host', '1.0.0', '适用Linux', '略', 'enable', '1', '{\n	\"ip\": \"*******\",\n	\"vars\": [\n             {\"key\": \"action\",\"value\": \"1\"}\n       ]\n}', '[\n{\n	\"vars\": [\n             {\"key\": \"action\",\"value\": \"1\"}\n       ]\n}\n]', '#!/bin/sh\n#version 1.2.3 modify at 20210412\n#sh cmd.sh \naction=<##action##>\necho \"\"\necho \"########## check profile timeout\"\nc=`cat /etc/profile|grep -v \"#\"|grep TMOUT|wc -l`\nif [[ $c -eq 0 ]];then\n	echo \"failed\"\n	if [[ $action -eq 1 ]];then\n		echo \"TMOUT=180\">>/etc/profile\n		echo \"export TMOUT\">>/etc/profile\n		echo \"set success\"\n	fi\nelse\n	echo \"success\"\nfi\necho \"########## check rsyslog\"\nif [[ -f /etc/rsyslog.conf ]];then\n	logcnt=`cat /etc/rsyslog.conf|grep -v \"#\"|grep ***********|wc -l`\n	if [[ $logcnt -eq 0 ]];then\n		echo \"failed\"\n		if [[ $action -eq 1 ]];then\n			echo \"*.* @***********\">>/etc/rsyslog.conf\n			echo \"set success\"\n		fi\n	else\n		echo \"success\" \n	fi\nfi\necho \"########## check system-auth\"\nif [[ -f /etc/pam.d/system-auth ]];then\n	authcnt=`cat /etc/pam.d/system-auth|grep pam_tally.so|wc -l`\n	if [[ $authcnt -eq 0 ]];then\n		echo \"failed\"\n		if [[ $action -eq 1 ]];then\n			echo \"auth required /lib/security/\\$ISA/pam_tally.so onerr=fail no_magic_root      \">>/etc/pam.d/system-auth\n			echo \"account required /lib/security/\\$ISA/pam_tally.so deny=6 no_magic_root reset \">>/etc/pam.d/system-auth\n			echo \"password requisite  pam_cracklib.so try_first_pass retry=3 difok=3 minlen=8 dcredit=-1 ucredit=-1 lcredit=-1 ocredit=-1\" >>/etc/pam.d/system-auth\n			echo \"password sufficient pam_unix.so sha512 shadow nullok try_first_pass use_authtok remember=5\" >>/etc/pam.d/system-auth\n			echo \"set success\"\n		fi\n	else\n		echo \"success\"\n	fi\nfi\necho \"########## check PASS_MAX_DAYS\"\ndvalue=`cat /etc/login.defs|grep -v \"#\"|grep PASS_MAX_DAYS|awk \'{print $NF}\'`\nif [[ $dvalue -eq 90 ]];then\n	echo \"success\"\nelse\n	echo \"failed\"\n	if [[ $action -eq 1 ]];then\n		sed -i \'/PASS_MAX_DAYS/d\' /etc/login.defs\n		echo \"PASS_MAX_DAYS 90\">>/etc/login.defs\n		echo \"set success\"\n	fi\nfi\necho \"########## check PASS_MIN_LEN\"\ndvalue=`cat /etc/login.defs|grep -v \"#\"|grep PASS_MIN_LEN|awk \'{print $NF}\'`\nif [[ $dvalue -eq 8 ]];then\n	echo \"success\"\nelse\n	echo \"failed\"\n	if [[ $action -eq 1 ]];then\n		sed -i \'/PASS_MIN_LEN/d\' /etc/login.defs\n		echo \"PASS_MIN_LEN 8\">>/etc/login.defs\n		echo \"set success\"\n	fi\nfi\necho \"########## check zabbix agent autostart\"\nzpath=`which zabbix_agentd 2>&1>/dev/null`\nif [[ $? -eq 0 ]];then\n	echo \"success\"\n	zcnt=`cat /etc/rc.d/rc.local|grep zabbix|wc -l`\n	if [[ $zcnt -eq 0 ]];then\n		chmod +x /etc/rc.d/rc.local\n		echo \"$zpath\">>/etc/rc.d/rc.local\n	fi\nelse\n    echo \"failed\"\nfi\necho \"########## check selinux\"\nsediscnt=`cat /etc/selinux/config |grep -v \"#\"|grep disabled|wc -l`\nif [[ $sediscnt -eq 1 ]];then\n	echo \"success\"\nelse\n	echo \"failed\"\n	if [[ $action -eq 1 ]];then\n		sed -i \'s/SELINUX=enforcing/SELINUX=disabled/\' /etc/selinux/config\n	fi\nfi\necho \"########## check /etc/rc.d/rc.local\"\ncat /etc/rc.d/rc.local|grep -v \"#\"|grep -v \"lock/subsys\"\necho \"########## check rhost\"\necho \"hosts.allow list\"\ncat /etc/hosts.allow|grep -v \"#\"\n#echo \"sshd:*********:allow\">>/etc/hosts.allow\necho \"hosts.allow deny\"\ncat /etc/hosts.deny|grep -v \"#\"\n#echo \"sshd:all:deny\">>/etc/hosts.deny\necho \"########## check port\"\nnetstat -ant|grep LISTEN\nexit 0\n\n', 'ops_tool', '', NULL, NULL, '110588348101165911', '2023-09-01 18:59:34', 1, '110588348101165911', '2023-09-01 18:59:34', 8, 'T001'),
('T001_modify_root', 'Linux超级管理员账户密码重置(root)', 'enable', 'host', '1.0', 'linux修改root密码', '略', 'enable', '1', '{}', '[]', '#!/bin/bash\n#echo \'oracle123456\'  |passwd --stdin root\necho \"success\"\nexit 0', 'ops_tool', '', NULL, NULL, '110588348101165911', '2023-09-01 19:14:00', 0, NULL, NULL, 7, 'T001'),
('T001_mysql', 'Linux部署MySQL', 'enable', 'host', '1.0', 'Linux 7.9环境,mysql版本5.7.43 /app/mysql目录', '略', 'enable', '1', '{}', '[]', '#!/bin/sh\ncd /tmp\nsh deploy_mysql.sh mysql.tar.gz /app/mysql P@ssw0rd123456\nexit 0', 'ops_tool', NULL, '110588348101165911', '2023-09-01 18:59:50', '110588348101165911', '2023-09-02 09:17:28', 0, NULL, NULL, 5, 'T001'),
('T001_nginx', 'Linux 部署Nginx', 'enable', 'host', '1.0', 'Linux 7.9环境,nginx 版本1.24 /app/nginx目录', '略', 'enable', '1', '{}', '[]', '#!/bin/sh\ncd /tmp\nsh deploy_nginx.sh nginx.tar.gz /app/nginx nginx\nexit 0', 'ops_tool', NULL, NULL, NULL, '110588348101165911', '2023-09-02 10:11:50', 0, NULL, NULL, 9, 'T001'),
('T001_redis', 'Linux部署Redis', 'enable', 'host', '1.0', 'Linux 7.9环境,redis 版本5.0.14 /app/redis目录', '略', 'enable', '1', '{}', '[]', '#!/bin/sh\ncd /tmp\nsh deploy_redis_single.sh redis.tar.gz /app/redis redis 6379\nexit 0', 'ops_tool', NULL, '110588348101165911', '2023-09-01 18:58:59', '110588348101165911', '2023-09-01 19:31:04', 0, NULL, NULL, 5, 'T001'),
('T001_test', 'Linux部署测试', 'enable', 'host', '1.0', '测试', NULL, 'enable', '1', '{}', '[]', '#!/bin/bash\n\necho \"test\"\nexit 0', 'ops_tool', NULL, '110588348101165911', '2023-09-01 19:13:47', '110588348101165911', '2023-09-01 19:18:54', 0, NULL, NULL, 2, 'T001'),
('T001_tomcat', 'Linux部署Tomcat', 'enable', 'host', '1.0', 'Linux 7.9环境,tomcat 版本9.0.80 /app/tomcat目录', '略', 'enable', '1', '{}', '[]', '#!/bin/sh\ncd /tmp\nsh deploy_tomcat.sh /app/tomcat tomcat.tar.gz app 8080\nexit 0\n', 'ops_tool', '', NULL, NULL, '110588348101165911', '2023-09-02 10:02:38', 0, NULL, NULL, 17, 'T001');
