
  PARTITION p204501 VALUES LESS THAN (to_days('2045-01-01')),
  PARTITION p204502 VALUES LESS THAN (to_days('2045-02-01')),
  PARTITION p204503 VALUES LESS THAN (to_days('2045-03-01')),
  PARTITION p204504 VALUES LESS THAN (to_days('2045-04-01')),
  PARTITION p204505 VALUES LESS THAN (to_days('2045-05-01')),
  PARTITION p204506 VALUES LESS THAN (to_days('2045-06-01')),
  PARTITION p204507 VALUES LESS THAN (to_days('2045-07-01')),
  PARTITION p204508 VALUES LESS THAN (to_days('2045-08-01')),
  PARTITION p204509 VALUES LESS THAN (to_days('2045-09-01')),
  PARTITION p204510 VALUES LESS THAN (to_days('2045-10-01')),
  PARTITION p204511 VALUES LESS THAN (to_days('2045-11-01')),
  PARTITION p204512 VALUES LESS THAN (to_days('2045-12-01')),

  
select
(select count(1) from oa_netdisk_file)oa_netdisk_file,
(select count(1) from eam_maintain_task where deleted=0 and tenant_id is not null) eam_maintain_task,
(select count(1) from eam_inspection_task where deleted=0 and tenant_id is not null) eam_inspection_task,
(select count(1) from eam_repair_order_act where deleted=0 and owner_type='part' and tenant_id is not null) eam_repair_order_act,
(select count(1) from eam_goods_stock where deleted=0 and owner_code='real_part') eam_goods_stock,
(select count(1) from eam_goods_stock where deleted=0 and owner_code='real_stock') eam_goods_stock,
(select count(1) from eam_asset where deleted=0 and owner_code='asset') eam_asset,
(select count(1) from ops_monitor_node where deleted=0) ops_monitor_node,
(select count(1) from ops_host where deleted=0) ops_host,
(select count(1) from hr_person where deleted=0) hr_person,
(select count(1) from cont_contract where deleted=0) cont_contract



--------------- 创建数据库

createdb.sql                创建数据库语句





--------------- 数据库导出的结构及数据

nextVal.sql                 系统是需要的存储过程
db.tar.gz                   整个数据库备份




--------------- 相关业务系统的配置sql

confuploadfille.sql         配置系统内部默认的模版、文件等
cleardata.sql               清除业务基本数据
clearhrm.sql                清除系统内部人员数据

setting_common.sql          通用的配置初始化
setting_hrm.sql             人事系统的配置初始化
setting_eam.sql             资产系统的配置初始化
setting_oa.sql              协同业务的配置初始化




