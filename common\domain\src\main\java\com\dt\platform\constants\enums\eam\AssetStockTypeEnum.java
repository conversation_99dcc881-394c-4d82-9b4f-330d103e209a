package com.dt.platform.constants.enums.eam;

import com.github.foxnic.api.constant.CodeTextEnum;


/**
 * @since 2021-07-24 10:25:37
 * <AUTHOR> , <EMAIL>
 * 从 select code,name from sys_dict WHERE deleted=0 and module in ('eam','hrm') 生成
 * 此文件由工具自动生成，请勿修改。若表结构变动，请使用工具重新生成
*/


public enum AssetStockTypeEnum implements CodeTextEnum {

	IN("in" , "入库"),
	OUT("out" , "出库"),
	USE("use" , "领用"),

	ALLOCATE("allocate" , "调拨"),
	ALLOCATE_IN("allocate_in" , "调进"),
	ALLOCATE_OUT("allocate_out" , "调出"),
	REPAIR_PART_OUT("repair_part_out" , "维修出货"),
	ADJUST("adjust" , "调整")
	;
	private String code;
	private String text;
	private AssetStockTypeEnum(String code, String text)  {
		this.code=code;
		this.text=text;
	}
	
	public String code() {
		return code;
	}
	
	public String text() {
		return text;
	}
}