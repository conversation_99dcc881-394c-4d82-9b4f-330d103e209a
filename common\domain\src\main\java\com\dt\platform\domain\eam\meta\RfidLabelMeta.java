package com.dt.platform.domain.eam.meta;

import com.github.foxnic.api.bean.BeanProperty;
import com.dt.platform.domain.eam.RfidLabel;
import java.util.Date;
import com.dt.platform.domain.eam.Asset;
import org.github.foxnic.web.domain.hrm.Employee;
import javax.persistence.Transient;



/**
 * <AUTHOR> , <EMAIL>
 * @since 2024-12-19 17:09:33
 * @sign 62AF274B4AC9C1017836DE9E84792DB4
 * 此文件由工具自动生成，请勿修改。若表结构或配置发生变动，请使用工具重新生成。
*/

public class RfidLabelMeta {
	
	/**
	 * 主键 , 类型: java.lang.String
	 */
	public static final String ID="id";
	
	/**
	 * 主键 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidLabel,java.lang.String> ID_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidLabel.class ,ID, java.lang.String.class, "主键", "主键", java.lang.String.class, null);
	
	/**
	 * RFID发卡 , 类型: java.lang.String
	 */
	public static final String RELEASE_ID="releaseId";
	
	/**
	 * RFID发卡 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidLabel,java.lang.String> RELEASE_ID_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidLabel.class ,RELEASE_ID, java.lang.String.class, "RFID发卡", "RFID发卡", java.lang.String.class, null);
	
	/**
	 * 状态 , 类型: java.lang.String
	 */
	public static final String STATUS="status";
	
	/**
	 * 状态 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidLabel,java.lang.String> STATUS_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidLabel.class ,STATUS, java.lang.String.class, "状态", "状态", java.lang.String.class, null);
	
	/**
	 * 资产 , 类型: java.lang.String
	 */
	public static final String ASSET_ID="assetId";
	
	/**
	 * 资产 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidLabel,java.lang.String> ASSET_ID_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidLabel.class ,ASSET_ID, java.lang.String.class, "资产", "资产", java.lang.String.class, null);
	
	/**
	 * 资产编码 , 类型: java.lang.String
	 */
	public static final String ASSET_CODE="assetCode";
	
	/**
	 * 资产编码 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidLabel,java.lang.String> ASSET_CODE_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidLabel.class ,ASSET_CODE, java.lang.String.class, "资产编码", "资产编码", java.lang.String.class, null);
	
	/**
	 * 标签 , 类型: java.lang.String
	 */
	public static final String LABEL="label";
	
	/**
	 * 标签 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidLabel,java.lang.String> LABEL_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidLabel.class ,LABEL, java.lang.String.class, "标签", "标签", java.lang.String.class, null);
	
	/**
	 * 发卡时间 , 类型: java.util.Date
	 */
	public static final String OPER_TIME="operTime";
	
	/**
	 * 发卡时间 , 类型: java.util.Date
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidLabel,java.util.Date> OPER_TIME_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidLabel.class ,OPER_TIME, java.util.Date.class, "发卡时间", "发卡时间", java.util.Date.class, null);
	
	/**
	 * 操作人员 , 类型: java.lang.String
	 */
	public static final String OPER_USER_ID="operUserId";
	
	/**
	 * 操作人员 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidLabel,java.lang.String> OPER_USER_ID_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidLabel.class ,OPER_USER_ID, java.lang.String.class, "操作人员", "操作人员", java.lang.String.class, null);
	
	/**
	 * 发卡结果 , 类型: java.lang.String
	 */
	public static final String OPER_RESULT="operResult";
	
	/**
	 * 发卡结果 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidLabel,java.lang.String> OPER_RESULT_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidLabel.class ,OPER_RESULT, java.lang.String.class, "发卡结果", "发卡结果", java.lang.String.class, null);
	
	/**
	 * 备注 , 类型: java.lang.String
	 */
	public static final String NOTES="notes";
	
	/**
	 * 备注 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidLabel,java.lang.String> NOTES_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidLabel.class ,NOTES, java.lang.String.class, "备注", "备注", java.lang.String.class, null);
	
	/**
	 * 创建人ID , 类型: java.lang.String
	 */
	public static final String CREATE_BY="createBy";
	
	/**
	 * 创建人ID , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidLabel,java.lang.String> CREATE_BY_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidLabel.class ,CREATE_BY, java.lang.String.class, "创建人ID", "创建人ID", java.lang.String.class, null);
	
	/**
	 * 创建时间 , 类型: java.util.Date
	 */
	public static final String CREATE_TIME="createTime";
	
	/**
	 * 创建时间 , 类型: java.util.Date
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidLabel,java.util.Date> CREATE_TIME_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidLabel.class ,CREATE_TIME, java.util.Date.class, "创建时间", "创建时间", java.util.Date.class, null);
	
	/**
	 * 修改人ID , 类型: java.lang.String
	 */
	public static final String UPDATE_BY="updateBy";
	
	/**
	 * 修改人ID , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidLabel,java.lang.String> UPDATE_BY_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidLabel.class ,UPDATE_BY, java.lang.String.class, "修改人ID", "修改人ID", java.lang.String.class, null);
	
	/**
	 * 修改时间 , 类型: java.util.Date
	 */
	public static final String UPDATE_TIME="updateTime";
	
	/**
	 * 修改时间 , 类型: java.util.Date
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidLabel,java.util.Date> UPDATE_TIME_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidLabel.class ,UPDATE_TIME, java.util.Date.class, "修改时间", "修改时间", java.util.Date.class, null);
	
	/**
	 * 是否已删除 , 类型: java.lang.Integer
	 */
	public static final String DELETED="deleted";
	
	/**
	 * 是否已删除 , 类型: java.lang.Integer
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidLabel,java.lang.Integer> DELETED_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidLabel.class ,DELETED, java.lang.Integer.class, "是否已删除", "是否已删除", java.lang.Integer.class, null);
	
	/**
	 * 删除人ID , 类型: java.lang.String
	 */
	public static final String DELETE_BY="deleteBy";
	
	/**
	 * 删除人ID , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidLabel,java.lang.String> DELETE_BY_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidLabel.class ,DELETE_BY, java.lang.String.class, "删除人ID", "删除人ID", java.lang.String.class, null);
	
	/**
	 * 删除时间 , 类型: java.util.Date
	 */
	public static final String DELETE_TIME="deleteTime";
	
	/**
	 * 删除时间 , 类型: java.util.Date
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidLabel,java.util.Date> DELETE_TIME_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidLabel.class ,DELETE_TIME, java.util.Date.class, "删除时间", "删除时间", java.util.Date.class, null);
	
	/**
	 * version , 类型: java.lang.Integer
	 */
	public static final String VERSION="version";
	
	/**
	 * version , 类型: java.lang.Integer
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidLabel,java.lang.Integer> VERSION_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidLabel.class ,VERSION, java.lang.Integer.class, "version", "version", java.lang.Integer.class, null);
	
	/**
	 * 资产 , 类型: com.dt.platform.domain.eam.Asset
	 */
	public static final String ASSET="asset";
	
	/**
	 * 资产 , 类型: com.dt.platform.domain.eam.Asset
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidLabel,com.dt.platform.domain.eam.Asset> ASSET_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidLabel.class ,ASSET, com.dt.platform.domain.eam.Asset.class, "资产", "资产", com.dt.platform.domain.eam.Asset.class, null);
	
	/**
	 * operUser , 类型: org.github.foxnic.web.domain.hrm.Employee
	 */
	public static final String OPER_USER="operUser";
	
	/**
	 * operUser , 类型: org.github.foxnic.web.domain.hrm.Employee
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidLabel,org.github.foxnic.web.domain.hrm.Employee> OPER_USER_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidLabel.class ,OPER_USER, org.github.foxnic.web.domain.hrm.Employee.class, "operUser", "operUser", org.github.foxnic.web.domain.hrm.Employee.class, null);
	
	/**
	 * 全部属性清单
	 */
	public static final String[] $PROPS={ ID , RELEASE_ID , STATUS , ASSET_ID , ASSET_CODE , LABEL , OPER_TIME , OPER_USER_ID , OPER_RESULT , NOTES , CREATE_BY , CREATE_TIME , UPDATE_BY , UPDATE_TIME , DELETED , DELETE_BY , DELETE_TIME , VERSION , ASSET , OPER_USER };
	
	/**
	 * 代理类
	 */
	public static class $$proxy$$ extends com.dt.platform.domain.eam.RfidLabel {

		private static final long serialVersionUID = 1L;

		
		/**
		 * 设置 主键
		 * @param id 主键
		 * @return 当前对象
		*/
		public RfidLabel setId(String id) {
			super.change(ID,super.getId(),id);
			super.setId(id);
			return this;
		}
		
		/**
		 * 设置 RFID发卡
		 * @param releaseId RFID发卡
		 * @return 当前对象
		*/
		public RfidLabel setReleaseId(String releaseId) {
			super.change(RELEASE_ID,super.getReleaseId(),releaseId);
			super.setReleaseId(releaseId);
			return this;
		}
		
		/**
		 * 设置 状态
		 * @param status 状态
		 * @return 当前对象
		*/
		public RfidLabel setStatus(String status) {
			super.change(STATUS,super.getStatus(),status);
			super.setStatus(status);
			return this;
		}
		
		/**
		 * 设置 资产
		 * @param assetId 资产
		 * @return 当前对象
		*/
		public RfidLabel setAssetId(String assetId) {
			super.change(ASSET_ID,super.getAssetId(),assetId);
			super.setAssetId(assetId);
			return this;
		}
		
		/**
		 * 设置 资产编码
		 * @param assetCode 资产编码
		 * @return 当前对象
		*/
		public RfidLabel setAssetCode(String assetCode) {
			super.change(ASSET_CODE,super.getAssetCode(),assetCode);
			super.setAssetCode(assetCode);
			return this;
		}
		
		/**
		 * 设置 标签
		 * @param label 标签
		 * @return 当前对象
		*/
		public RfidLabel setLabel(String label) {
			super.change(LABEL,super.getLabel(),label);
			super.setLabel(label);
			return this;
		}
		
		/**
		 * 设置 发卡时间
		 * @param operTime 发卡时间
		 * @return 当前对象
		*/
		public RfidLabel setOperTime(Date operTime) {
			super.change(OPER_TIME,super.getOperTime(),operTime);
			super.setOperTime(operTime);
			return this;
		}
		
		/**
		 * 设置 操作人员
		 * @param operUserId 操作人员
		 * @return 当前对象
		*/
		public RfidLabel setOperUserId(String operUserId) {
			super.change(OPER_USER_ID,super.getOperUserId(),operUserId);
			super.setOperUserId(operUserId);
			return this;
		}
		
		/**
		 * 设置 发卡结果
		 * @param operResult 发卡结果
		 * @return 当前对象
		*/
		public RfidLabel setOperResult(String operResult) {
			super.change(OPER_RESULT,super.getOperResult(),operResult);
			super.setOperResult(operResult);
			return this;
		}
		
		/**
		 * 设置 备注
		 * @param notes 备注
		 * @return 当前对象
		*/
		public RfidLabel setNotes(String notes) {
			super.change(NOTES,super.getNotes(),notes);
			super.setNotes(notes);
			return this;
		}
		
		/**
		 * 设置 创建人ID
		 * @param createBy 创建人ID
		 * @return 当前对象
		*/
		public RfidLabel setCreateBy(String createBy) {
			super.change(CREATE_BY,super.getCreateBy(),createBy);
			super.setCreateBy(createBy);
			return this;
		}
		
		/**
		 * 设置 创建时间
		 * @param createTime 创建时间
		 * @return 当前对象
		*/
		public RfidLabel setCreateTime(Date createTime) {
			super.change(CREATE_TIME,super.getCreateTime(),createTime);
			super.setCreateTime(createTime);
			return this;
		}
		
		/**
		 * 设置 修改人ID
		 * @param updateBy 修改人ID
		 * @return 当前对象
		*/
		public RfidLabel setUpdateBy(String updateBy) {
			super.change(UPDATE_BY,super.getUpdateBy(),updateBy);
			super.setUpdateBy(updateBy);
			return this;
		}
		
		/**
		 * 设置 修改时间
		 * @param updateTime 修改时间
		 * @return 当前对象
		*/
		public RfidLabel setUpdateTime(Date updateTime) {
			super.change(UPDATE_TIME,super.getUpdateTime(),updateTime);
			super.setUpdateTime(updateTime);
			return this;
		}
		
		/**
		 * 设置 是否已删除
		 * @param deleted 是否已删除
		 * @return 当前对象
		*/
		public RfidLabel setDeleted(Integer deleted) {
			super.change(DELETED,super.getDeleted(),deleted);
			super.setDeleted(deleted);
			return this;
		}
		
		/**
		 * 设置 删除人ID
		 * @param deleteBy 删除人ID
		 * @return 当前对象
		*/
		public RfidLabel setDeleteBy(String deleteBy) {
			super.change(DELETE_BY,super.getDeleteBy(),deleteBy);
			super.setDeleteBy(deleteBy);
			return this;
		}
		
		/**
		 * 设置 删除时间
		 * @param deleteTime 删除时间
		 * @return 当前对象
		*/
		public RfidLabel setDeleteTime(Date deleteTime) {
			super.change(DELETE_TIME,super.getDeleteTime(),deleteTime);
			super.setDeleteTime(deleteTime);
			return this;
		}
		
		/**
		 * 设置 version
		 * @param version version
		 * @return 当前对象
		*/
		public RfidLabel setVersion(Integer version) {
			super.change(VERSION,super.getVersion(),version);
			super.setVersion(version);
			return this;
		}
		
		/**
		 * 设置 资产
		 * @param asset 资产
		 * @return 当前对象
		*/
		public RfidLabel setAsset(Asset asset) {
			super.change(ASSET,super.getAsset(),asset);
			super.setAsset(asset);
			return this;
		}
		
		/**
		 * 设置 operUser
		 * @param operUser operUser
		 * @return 当前对象
		*/
		public RfidLabel setOperUser(Employee operUser) {
			super.change(OPER_USER,super.getOperUser(),operUser);
			super.setOperUser(operUser);
			return this;
		}

		/**
		 * 克隆当前对象
		*/
		@Transient
		public RfidLabel clone() {
			return duplicate(true);
		}

		/**
		 * 复制当前对象
		 * @param all 是否复制全部属性，当 false 时，仅复制来自数据表的属性
		*/
		@Transient
		public RfidLabel duplicate(boolean all) {
			$$proxy$$ inst=new $$proxy$$();
			inst.setOperUserId(this.getOperUserId());
			inst.setAssetCode(this.getAssetCode());
			inst.setNotes(this.getNotes());
			inst.setUpdateTime(this.getUpdateTime());
			inst.setLabel(this.getLabel());
			inst.setVersion(this.getVersion());
			inst.setCreateBy(this.getCreateBy());
			inst.setOperResult(this.getOperResult());
			inst.setDeleted(this.getDeleted());
			inst.setReleaseId(this.getReleaseId());
			inst.setCreateTime(this.getCreateTime());
			inst.setUpdateBy(this.getUpdateBy());
			inst.setDeleteTime(this.getDeleteTime());
			inst.setAssetId(this.getAssetId());
			inst.setDeleteBy(this.getDeleteBy());
			inst.setId(this.getId());
			inst.setStatus(this.getStatus());
			inst.setOperTime(this.getOperTime());
			if(all) {
				inst.setAsset(this.getAsset());
				inst.setOperUser(this.getOperUser());
			}
			inst.clearModifies();
			return inst;
		}

	}
}