from docx import Document
import os
import uuid
from lxml import etree
from bs4 import BeautifulSoup

def docx_to_html(docx_path, output_dir):
    try:
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        img_dir = os.path.join(output_dir, 'images')
        os.makedirs(img_dir, exist_ok=True)

        # 读取Word文档
        doc = Document(docx_path)

        # 响应式HTML模板
        html_template = """<!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, minimum-scale=1.0">
            <title>文档转换系统</title>
            <style>
                :root {
                    --mobile-breakpoint: 768px;
                    --toc-width: 280px;
                }

                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                    font-family: -apple-system, BlinkMacSystemFont,
                               'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell,
                               sans-serif;
                }

                body {
                    display: flex;
                    min-height: 100vh;
                    background: #f8f9fa;
                }

                /* 桌面布局 */
                #toc {
                    width: var(--toc-width);
                    height: 100vh;
                    overflow-y: auto;
                    padding: 20px 15px;
                    background: white;
                    box-shadow: 2px 0 8px rgba(0,0,0,0.08);
                    position: fixed;
                    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                #content {
                    flex: 1;
                    margin-left: var(--toc-width);
                    padding: 30px 5%;
                    background: white;
                    min-height: 100vh;
                }

                /* 移动端布局 */
                @media (max-width: 768px) {
                    body {
                        flex-direction: column;
                        overflow-x: hidden;
                    }

                    #toc {
                        width: 85%;
                        height: 100vh;
                        transform: translateX(-100%);
                        z-index: 1000;
                        box-shadow: 2px 0 15px rgba(0,0,0,0.15);
                    }

                    #toc.active {
                        transform: translateX(0);
                    }

                    #content {
                        margin-left: 0;
                        width: 100%;
                        padding: 20px;
                        padding-top: 60px;
                    }

                    .mobile-menu {
                        display: block;
                        position: fixed;
                        top: 15px;
                        left: 15px;
                        padding: 12px;
                        background: #3498db;
                        color: white;
                        border: none;
                        border-radius: 8px;
                        z-index: 1001;
                        cursor: pointer;
                        box-shadow: 0 2px 6px rgba(0,0,0,0.15);
                    }
                }

                /* 目录样式 */
                .toc-header {
                    font-size: 1.25rem;
                    color: #2c3e50;
                    margin-bottom: 1.5rem;
                    font-weight: 600;
                    padding-bottom: 0.5rem;
                    border-bottom: 1px solid #eee;
                }

                .toc-item {
                    margin: 0.5rem 0;
                }

                .toc-item.level1 { margin-left: 0; }
                .toc-item.level2 { margin-left: 20px; }
                .toc-item.level3 { margin-left: 40px; }
                .toc-item.level4 { margin-left: 60px; }

                .toc-item a {
                    display: block;
                    padding: 0.8rem 1rem;
                    color: #4a5568;
                    text-decoration: none;
                    border-radius: 6px;
                    transition: all 0.2s ease;
                }

                .toc-item a:hover {
                    background: #f7fafc;
                }

                /* 内容样式 */
                h1 { font-size: 2rem; margin: 2rem 0 1.5rem; }
                h2 { font-size: 1.75rem; margin: 1.75rem 0 1.25rem; }
                h3 { font-size: 1.5rem; margin: 1.5rem 0 1rem; }
                p {
                    line-height: 1.75;
                    color: #4a5568;
                    margin: 1.25rem 0;
                    font-size: 1rem;
                }

                img {
                    max-width: 100%;
                    height: auto;
                    margin: 2rem 0;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                }
            </style>
        </head>
        <body>
            <button class="mobile-menu" onclick="toggleToc()">☰ 目录</button>
            <div id="toc">
                <div class="toc-header">文档目录</div>
            </div>
            <div id="content"></div>

            <script>
                let tocVisible = false;

                function toggleToc() {
                    const toc = document.getElementById('toc');
                    tocVisible = !tocVisible;
                    toc.classList.toggle('active');
                }

                document.addEventListener('click', (e) => {
                    const toc = document.getElementById('toc');
                    const menuBtn = document.querySelector('.mobile-menu');

                    if (window.innerWidth <= 768 &&
                        !toc.contains(e.target) &&
                        e.target !== menuBtn &&
                        tocVisible) {
                        toggleToc();
                    }
                });

                document.querySelectorAll('#toc a').forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const target = document.querySelector(this.getAttribute('href'));
                        if (target) {
                            const offset = window.innerWidth <= 768 ? 80 : 120;
                            window.scrollTo({
                                top: target.offsetTop - offset,
                                behavior: 'smooth'
                            });
                            if (window.innerWidth <= 768) toggleToc();
                        }
                    });
                });
            </script>
        </body>
        </html>
        """

        soup = BeautifulSoup(html_template, 'html.parser')
        content_div = soup.find('div', id='content')
        toc_div = soup.find('div', id='toc')

        # 添加目录标题
        toc_header = soup.new_tag('div', attrs={'class': 'toc-header'})
        toc_header.string = "文档目录"
        toc_div.append(toc_header)

        # 处理文档内容
        for para in doc.paragraphs:
            # 提取图片
            for run in para.runs:
                img_path = extract_image(run, img_dir)
                if img_path:
                    img_tag = soup.new_tag('img', attrs={
                        'src': os.path.join('images', os.path.basename(img_path)),
                        'loading': 'lazy'
                    })
                    content_div.append(img_tag)

            # 处理文本内容
            text = para.text.strip()
            if not text:
                continue

            style = para.style.name
            if style.startswith('Heading'):
                level = min(int(style.split()[-1]), 4)
                anchor_id = f"h{level}-{uuid.uuid4().hex}"

                # 创建标题标签
                tag = soup.new_tag(f'h{level}', attrs={'id': anchor_id})
                tag.string = text
                content_div.append(tag)

                # 创建目录项
                toc_item = soup.new_tag('div', attrs={'class': f'toc-item level{level}'})
                link = soup.new_tag('a', attrs={'href': f'#{anchor_id}'})
                link.string = text
                toc_item.append(link)
                toc_div.append(toc_item)
            else:
                p_tag = soup.new_tag('p')
                p_tag.string = text
                content_div.append(p_tag)

        # 保存HTML文件
        html_path = os.path.join(output_dir, 'output.html')
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(str(soup.prettify()))

        print(f"转换成功！\nHTML文件: {html_path}\n图片目录: {img_dir}")

    except Exception as e:
        print(f"转换失败: {str(e)}")

def extract_image(run, img_dir):
    try:
        element = run._element
        xml_str = etree.tostring(element, encoding='unicode')

        namespaces = {
            'pic': "http://schemas.openxmlformats.org/drawingml/2006/picture",
            'a': "http://schemas.openxmlformats.org/drawingml/2006/main",
            'r': "http://schemas.openxmlformats.org/officeDocument/2006/relationships"
        }

        tree = etree.fromstring(xml_str)
        pics = tree.xpath('.//pic:pic', namespaces=namespaces)

        for pic in pics:
            blips = pic.xpath('.//a:blip', namespaces=namespaces)
            if blips:
                embed_id = blips[0].get("{http://schemas.openxmlformats.org/officeDocument/2006/relationships}embed")
                return save_image(embed_id, run, img_dir)
        return None
    except Exception as e:
        print(f"图片提取失败: {str(e)}")
        return None

def save_image(embed_id, run, img_dir):
    try:
        part = run.part
        if embed_id in part.related_parts:
            image_part = part.related_parts[embed_id]
            img_bytes = image_part.blob

            img_name = f"image_{uuid.uuid4().hex}.png"
            img_path = os.path.join(img_dir, img_name)

            with open(img_path, 'wb') as f:
                f.write(img_bytes)
            return img_path
        return None
    except Exception as e:
        print(f"图片保存失败: {str(e)}")
        return None

if __name__ == "__main__":
    docx_to_html('input.docx', 'output')