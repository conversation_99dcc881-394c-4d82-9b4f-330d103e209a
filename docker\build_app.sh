#!/bin/sh
#################################################################################
# put /app/app/app目录下
#
#################################################################################
cur_dir=$(cd `dirname $0`; pwd)
cd $cur_dir

docker_image_version=2.9.2
if [[ -n $1 ]];then
  docker_image_version=$1
fi
docker_image_flag=fapp_app_${docker_image_version}
echo "docker image:$docker_image_flag"

###### 修改配置文件
nginx_ip=***********
bpm_ip=***********
app_ip=***********
db_ip=***********
redis_ip=***********
app_oss=***********
app_port=8089
bpm_port=8099
db_port=3306
db_name=app
db_user=root
db_pwd=P@ssw0rd123456
storage=minio
##修改application.yml文件
application_yml=application_tpl_docker.yml
if [[ ! -f application_tpl.yml ]];then
  echo "application_tpl.yml not found"
  exit 1
fi
cat application_tpl.yml>$application_yml

###########init oss data
cat << EOF >ossdata.sh
#!/bin/bash
sleep 60
bucket=appdata
oss_host=\`env|grep MINIO_URL|awk -F '=' '{print \$NF}'\`
oss_user=\`env|grep MINIO_ACCESS_KEY|grep -v MINIO_ACCESS_KEY_YML|awk -F '=' '{print \$NF}'\`
oss_pass=\`env|grep MINIO_SECRET_KEY|grep -v MINIO_SECRET_KEY_YML|awk -F '=' '{print \$NF}'\`
app_upload_dir=/app/app/app/upload_source
mc alias set local_app \$oss_host \$oss_user \$oss_pass
echo "list bucket">>/init.log
mc ls local_app/\$bucket
res=\`echo \$?\`
if [ \$res -ne 0 ]; then
  echo "bucket not exists"
  echo "bucket not exists">>/init.log
  mc mb local_app/\$bucket
  mc cp \$app_upload_dir/media      local_app/\$bucket --recursive
  mc cp \$app_upload_dir/image      local_app/\$bucket --recursive
  mc cp \$app_upload_dir/script     local_app/\$bucket --recursive
  mc cp \$app_upload_dir/software   local_app/\$bucket --recursive
  mc cp \$app_upload_dir/docs       local_app/\$bucket --recursive
  mc cp \$app_upload_dir/collect    local_app/\$bucket --recursive
  mc cp \$app_upload_dir/tpl        local_app/\$bucket --recursive
  if [ -f /local_app-iam-info.zip ]; then
    echo "iam info exists,start to import ">>/init.log
    mc admin cluster iam import local_app /local_app-iam-info.zip
    rm -rf local_app-iam-info.zip
  fi
fi
echo "bucket finish">>/init.log
exit 0
EOF
chmod 777 ossdata.sh

##生成entrypoint.sh文件
cat << EOF > entrypoint.sh
#!/bin/bash

app_ip=***********
app_port=8089
bpm_port=8099
bpm_ip=\`env|grep BPM_HOST_IP|awk -F '=' '{print \$NF}'\`
db_ip=\`env|grep MYSQL_HOST_IP|awk -F '=' '{print \$NF}'\`
db_ip_cnt=`cat /etc/hosts|grep db.app.dt|wc -l`
if [ \$db_ip_cnt -gt 0 ];then
  db_ip=db.app.dt
fi
db_port=\`env|grep MYSQL_HOST_PORT|awk -F '=' '{print \$NF}'\`
db_name=\`env|grep MYSQL_DATABASE|awk -F '=' '{print \$NF}'\`
db_user=\`env|grep MYSQL_USER|awk -F '=' '{print \$NF}'\`
db_pwd=\`env|grep MYSQL_PASSWORD|awk -F '=' '{print \$NF}'\`
minio_url=\`env|grep MINIO_URL|awk -F '=' '{print \$NF}'\`
storage_type=\`env|grep STORAGE_TYPE|awk -F '=' '{print \$NF}'\`
access_key_yml=\`env|grep MINIO_ACCESS_KEY_YML|awk -F '=' '{print \$NF}'\`
secret_key_yml=\`env|grep MINIO_SECRET_KEY_YML|awk -F '=' '{print \$NF}'\`

sed -i "s/APP_WEB_PORT/\$app_port/g"                             /app/app/app/application.yml
sed -i "s/APP_DB_PORT/\$db_port/g"                               /app/app/app/application.yml
sed -i "s/APP_DB_NAME/\$db_name/g"                               /app/app/app/application.yml
sed -i "s/APP_DB_USERNAME/\$db_user/g"                           /app/app/app/application.yml
sed -i "s/APP_DB_PASSWORD/\$db_pwd/g"                            /app/app/app/application.yml
sed -i "s/127.0.0.1:3306/\${db_ip}:\${db_port}/g"                /app/app/app/application.yml
sed -i "s/127.0.0.1:3306/\${db_ip}:\${db_port}/g"                /app/app/app/application.yml
sed -i "s/127.0.0.1:8089/\${app_ip}:\${app_port}/g"              /app/app/app/application.yml
sed -i "s/127.0.0.1:8099/\${bpm_ip}:\${bpm_port}/g"              /app/app/app/application.yml
sed -i "s/APP_STORAGE_DATA/\$storage_type/g"                     /app/app/app/application.yml
sed -i "s/APP_STORAGE_NETDISK/\$storage_type/g"                  /app/app/app/application.yml
sed -i "s@APP_STORAGE_MINIO_URL@\$minio_url@g"                   /app/app/app/application.yml
sed -i "s/MINIO_ACCESS_KEY_YML/\$access_key_yml/g"               /app/app/app/application.yml
sed -i "s/MINIO_SECRET_KEY_YML/\$secret_key_yml/g"               /app/app/app/application.yml


touch cmd.conf
echo "************************************************************************************************************************************************************************************************************* count(1) cnt from information_schema.tables where table_schema @eq@ '\$db_name' and table_name like '%sys_%'">/mysql.node
#connect check
while true
do
  java -jar /ops.jar -n /mysql.node -mysql connectCheck >/tmp/connect.log
  connectCnt=\`cat /tmp/connect.log |grep 20|wc -l\`
  echo "Current Connect Status:\$connectCnt"
  d=\`date\`
  echo \$d>>/init.log
  echo "Current Connect Status:\$connectCnt" >>/init.log
  if [ \$connectCnt -gt 0 ]; then
    echo "mysql connect success!"
    break;
  fi
  sleep 5
done

#db check
while true
do
  java -jar /ops.jar -n /mysql.node -mysql exec >/tmp/db.log
  dbCnt=\`cat /tmp/db.log |grep -v select|grep cnt|awk -F ':' '{print \$NF}'\`
  echo "Current Table Count Status:\$dbCnt"
  d=\`date\`
  echo \$d>>/init.log
  echo "Current Table Count Status:\$dbCnt" >>/init.log
  if [ \$dbCnt -gt 100 ]; then
    echo "mysql table success!"
    break;
  fi
  sleep 5
done

tsize=\`du -sm /app/app/app/upload|awk '{print \$1}'\`
if [[ \$tsize -lt 5 ]];then
  cp -r  /app/app/app/upload_source/* /app/app/app/upload
fi

nohup sh /ossdata.sh &

java -noverify -Dspring.config.location=/app/app/app/application.yml -Dfile.encoding=UTF-8 -Djava.io.tmpdir=/app/app/app/tmp -Dloader.path=/app/app/app/lib -jar /app/app/app/app.jar
EOF

if [[ -f ops.jar ]];then
  rm -rf ops.jar
fi
cp /app/app/bin/local_app-iam-info.zip local_app-iam-info.zip
cp /app/app/bin/ops.jar.1 ops.jar
cp /app/app/bin/mc.tar.gz mc.tar.gz
tar xvf mc.tar.gz
chmod 777 mc
##生成Dockerfile文件
cat << EOF > Dockerfile
FROM registry.cn-hangzhou.aliyuncs.com/lank/jdk:s_1.8.342
MAINTAINER APP
USER root
ADD local_app-iam-info.zip  /
ADD mc                      /sbin
ADD ossdata.sh              /ossdata.sh
RUN mkdir -p                /app/app/app
RUN mkdir -p                /app/app/app/tmp
RUN mkdir -p                /app/app/app/app_logs
RUN mkdir -p                /app/app/app/lib
RUN mkdir -p                /app/app/app/netdisk
RUN mkdir -p                /app/app/app/upload
ADD ops.jar                 /ops.jar
ADD app.jar                 /app/app/app/app.jar
ADD lib/*.jar               /app/app/app/lib/
ADD upload                  /app/app/app/upload_source
ADD $application_yml        /app/app/app/application.yml
COPY entrypoint.sh          /entrypoint.sh
RUN chmod +x                /entrypoint.sh
ENTRYPOINT ["/entrypoint.sh"]
EOF

##build 生成image
docker build ./ -t registry.cn-hangzhou.aliyuncs.com/lank/app:$docker_image_flag

rm -rf application_tpl_docker.yml
rm -rf entrypoint.sh
rm -rf ossdata.sh
rm -rf ops.jar
rm -rf Dockerfile
rm -rf mc.tar.gz
rm -rf mc
exit 0

#docker run -it -v /app/test:/app/app/app/upload  -d docker.io/openjdk:app10
