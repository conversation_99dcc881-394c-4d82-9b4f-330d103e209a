######App config
CUSTOMER_NAME=APP
APP_UID=app
APP_DIR=/app/app
APP_WEB_PORT=8089
APP_STATUS=enable
APP_NAME=app.jar
BPM_STATUS=enable
BPM_NAME=bpm.jar
JOB_STATUS=disable
JOB_NAME=job.jar
APP_NAME=app.jar
JAVA=java
######OPS
DATA_CLEAR=1
#1 will overwrite,0 not update
APP_TPL_UPDATE=1
######APP MYSQL
MYSQL=/app/db/mysql/bin/mysql
MYSQL_DUMP=/app/db/mysql/bin/mysqldump
MYSQL_ADMIN=/app/db/mysql/bin/mysqladmin
DB_HOST=127.0.0.1
DB_PORT=3306
DB_NAME=foxnic
DB_USER=root
DB_PWD=root_pwd
#backup
BACKUP_DIR=/app/app/backup





