#!/bin/sh
#################################################################################
# /app/app/tengine目录下
#
#################################################################################
cur_dir=$(cd `dirname $0`; pwd)
cd $cur_dir
echo "cur_dir:$cur_dir"
docker_image_version=2.9.2
if [[ -n $1 ]];then
  docker_image_version=$1
fi
docker_image_flag=fapp_h5_${docker_image_version}
echo "docker image:$docker_image_flag"
app_ip=***********
app_port=8089
app_api=http://${app_ip}:${app_port}
cd $cur_dir
##生成Dockerfile文件
cat << EOF > nginx_docker.conf
worker_processes auto;
worker_rlimit_nofile 65535;
events {
    use epoll;
    multi_accept on;
    worker_connections 1024;
}
http {
    include       mime.types;
    default_type  application/octet-stream;
    log_format  main  '\$remote_addr - \$remote_user [\$time_local] "\$request" '
                  '\$status \$body_bytes_sent "\$http_referer" '
                  '"\$http_user_agent" "\$http_x_forwarded_for"';
    access_log  /var/log/nginx/access.log main;
    error_log   /var/log/nginx/error.log warn;
    proxy_buffering on;
    proxy_buffer_size 8k;
    proxy_buffers 4 32k;
    proxy_busy_buffers_size 64K;
    proxy_temp_path /var/nginxtemp 1 2;
    proxy_max_temp_file_size 20m;
    proxy_temp_file_write_size 256k;
    #cache
    proxy_cache_path /var/nginxcache levels=1:1 keys_zone=cache_one:200m inactive=3d max_size=30g;
    server_tokens off;
    keepalive_timeout 60;
    keepalive_requests 1024;
    client_header_buffer_size 512k;
    client_body_buffer_size 1m;
    client_max_body_size 256m;
    sendfile on;
    #big file
    tcp_nopush on;
    #small file
    tcp_nodelay on;
    open_file_cache max=65535 inactive=60s;
    open_file_cache_valid 60s;
    open_file_cache_min_uses 1;
    open_file_cache_errors on;
    gzip on;
    gzip_static on;
    gzip_min_length 1k;
    gzip_comp_level 9;
    gzip_buffers 16 8k;
    gzip_types image/jpeg image/gif image/x-icon image/png application/font-woff2 text/plain text/css application/x-font-ttf application/json application/x-javascript application/javascript text/javascript;
    gzip_vary on;
    gzip_proxied any;
    gzip_disable "MSIE [1-6]\\.";
    gzip_http_version 1.1;
    add_header Access-Control-Allow-Origin *;
    server {
        listen *:8899 default_server;
        server_name resource.xyjm.store;
        location /ngx_status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            deny all;
          }
    }
    server {
        listen 8091 ssl;
        server_name resource.xyjm.store;
        root  /nginx/html;
        index index.html index.htm;
        ssl_certificate /nginx/cert/resource.xyjm.store.pem;
        ssl_certificate_key /nginx/cert/resource.xyjm.store.key;
        ssl_session_timeout 5m;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
        ssl_prefer_server_ciphers on;
        location / {
            index index.html index.htm;
            proxy_redirect http:// https://;
            proxy_set_header Host \$host:\$server_port;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            add_header X-Cache-Status \$upstream_cache_status;
            proxy_cache_key \$host\$uri\$is_args\$args;
            proxy_cache cache_one;
            proxy_cache_valid 200 7d;
            proxy_cache_valid 302 1h;
            proxy_cache_valid 301 1d;
            proxy_cache_valid any 1m;
            expires 30d;
         }
        location /service  {
            proxy_redirect http:// https://;
            proxy_set_header Host \$host:\$server_port;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_pass $app_api;
        }
        location /security {
            proxy_redirect http:// https://;
            proxy_set_header Host \$host:\$server_port;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_pass $app_api;
        }
    }
}
EOF

##生成Dockerfile文件
if [[ -f "resource.xyjm.store.pem" ]];then
  rm -rf resource.xyjm.store.pem
fi
if [[ -f "resource.xyjm.store.key" ]];then
  rm -rf resource.xyjm.store.key
fi

#cert auto create
if [[ -f "/app/app/tengine/cert/resource.xyjm.store.pem" ]];then
  rm -rf /app/app/tengine/cert/resource.xyjm.store.pem
fi
if [[ -f "/app/app/tengine/cert/resource.xyjm.store.key" ]];then
  rm -rf /app/app/tengine/cert/resource.xyjm.store.key
fi
cp /root/.acme.sh/resource.xyjm.store/resource.xyjm.store.cer /app/app/tengine/cert/resource.xyjm.store.pem
cp /root/.acme.sh/resource.xyjm.store/resource.xyjm.store.key /app/app/tengine/cert/resource.xyjm.store.key
cp cert/resource.xyjm.store.pem resource.xyjm.store.pem
cp cert/resource.xyjm.store.key resource.xyjm.store.key
rm -rf nginx
cp /usr/sbin/nginx .
chmod 755 nginx
if [[ -d "html" ]];then
  rm -rf html
fi
mkdir html
cp h5.tar.gz.1 html
cd html
tar xvf h5.tar.gz.1
cd $cur_dir
cat << EOF > Dockerfile
FROM registry.cn-hangzhou.aliyuncs.com/lank/nginx:s_1.21.5
MAINTAINER NGINX
RUN mkdir -p              /nginx/cert
RUN mkdir -p              /nginx/html
RUN mkdir -p              /var/log/nginx
COPY nginx_docker.conf    /etc/nginx/nginx.conf
ADD resource.xyjm.store.pem   /nginx/cert
ADD resource.xyjm.store.key   /nginx/cert
ADD html                      /nginx/html
EOF
##build 生成image
docker build ./ -t registry.cn-hangzhou.aliyuncs.com/lank/nginx:$docker_image_flag
exit 0



