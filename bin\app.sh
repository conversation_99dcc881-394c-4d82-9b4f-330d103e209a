#!/bin/sh
#####################################################################
# Script Help:
#    sh app.sh [start][stop][restart][status]  [app][bpm][job][db]"
#####################################################################
####################### Configure ###################################
help(){
    echo "Usage::"
    echo "   sh app.sh [start][stop][restart][status]  [app][bpm][job][db]"
    echo "Example:"
    echo "   sh app.sh restart app"
}

cur_dir=$(cd `dirname $0`; pwd)
app_conf="${cur_dir}/app.conf"
app_dir=$cur_dir/..
app_log=$app_dir/logs
if [[ ! -d "$app_dir/tmp" ]];then
  mkdir -p $app_dir/tmp
fi
####################### Java Environment ############################
JAVA=`cat $app_conf|grep -v "#"|grep JAVA=|awk -F "=" '{print $2}'`
#java_Xmx="-Xmx1024m"
java_Xmx=""
####################### App Environment ############################
if [[ ! -d $app_dir ]];then
  echo "App_dir:$app_dir not exist"
  help
  exit 1;
fi

cDir="$app_dir/logs
$app_dir/update
"
for cdir in $cDir
do
  if [[ ! -d "$cdir" ]];then
     mkdir -p "$cdir"
  fi
done
####################### Parameter ####################################
app="unknow"
if [[ ! -n $1 ]];then
  help
  exit 1
fi
if [[ -n $2 ]];then
  app=$2
fi
app_name=${app}.jar
app_log_file=$app_log/${app_name}.log
if [[ $app_name = "job.jar" ]];then
  echo "不需要运行job.jar"
  exit 0
fi
if [[ $app != "db" ]];then
  if [[ ! -f "$app_dir/$app/$app_name" ]];then
    help
    echo "jar not exist,jar:$app_dir/app/$app/$app_name"
    exit 1
  fi
fi
app_uid=`cat $app_conf|grep -v "#"|grep APP_UID=|awk -F "=" '{print $2}'`
app_process_mark="app01_${app_name}_${app_uid}"
action="unknow"
if [[ -n $1 ]];then
  action=$1
fi
###########
#which $JAVA
javaChk=$?
if [[ $javaChk -ne 0 ]];then
  echo "Java not found!"
  exit 1
fi
#######################Function start stop ... #######################
env(){
  if [[ ! -d $app_dir ]];then
      mkdir -p $app_dir
  fi
  if [[ ! -d $app_log ]];then
      mkdir -p $app_log
  fi
}
start(){
  env
  #echo "Action Start"
  cd $app_dir
  pidcnt=`ps -ef|grep java|grep $app_process_mark|grep -v grep |awk '{print $2}'|wc -l`
  if [[ $pidcnt -ge 1 ]];then
    echo "$app process is already running,please first stop it."
  else
    cd $app_dir/$app
    application_yml="";
    if [[ -f "application.yml" ]];then
      application_yml="-Dspring.config.location=application.yml"
    fi
    nohup $JAVA -noverify -Dfile.encoding=UTF-8 $application_yml -Djava.io.tmpdir=$app_dir/tmp -Dloader.path=./lib/ $java_Xmx -jar $app_name -dprocess_Mark=$app_process_mark >$app_log_file 2>&1 &
    sleep 3
    pidlist2=`ps -ef|grep java|grep $app_process_mark|grep -v grep |awk '{print $2}'`
    pidcnt2=`ps -ef|grep java|grep $app_process_mark|grep -v grep |awk '{print $2}'|wc -l`
    if [[ $pidcnt2 -ge 1 ]];then
        echo "$app process start success,pid:$pidlist2"
    fi
  fi
}
stop(){
  #echo "Action Stop"
  pidlist=`ps -ef|grep java|grep $app_process_mark|grep -v grep |awk '{print $2}'`
  pidcnt=`ps -ef|grep java|grep $app_process_mark|grep -v grep |awk '{print $2}'|wc -l`
  if [[ $pidcnt -ge 1 ]];then
      echo "$app running process number:$pidcnt"
      for pid in $pidlist
      do
          echo "start to kill process,pid:$pid"
          kill -9 $pid
      done
      echo "$app process stop success"
  else
      echo "$app is not running"
  fi
}

function stopMysql(){
  cnt=`ps -ef|grep mysqld|grep -v grep |grep my_plat.cnf|wc -l`
  if [[ $cnt -gt 0 ]];then
    ps -ef|grep mysqld|grep -v grep |grep my_plat.cnf|awk '{print $2}'|xargs kill -9
    sleep 2
  fi
  echo "stop mysql success!"
}
function startMysql(){
  cd /tmp
  su - mysql -c "cd /tmp/;nohup /app/db/mysql/bin/mysqld_safe --defaults-file=/etc/my_plat.cnf &"
  cnt=`ps -ef|grep mysqld|grep -v grep |grep my_plat.cnf`
  sleep 2
  echo "start mysql success!"
}
restart(){
  stop
  sleep 2
  start
}
status(){
  #echo "Action Status"
  pidlist=`ps -ef|grep java|grep $app_process_mark|grep -v grep |awk '{print $2}'`
  pidcnt=`ps -ef|grep java|grep $app_process_mark|grep -v grep |awk '{print $2}'|wc -l`
  if [[ $pidcnt -ge 1 ]];then
    echo "$app process is running,pid:$pidlist"
  else
    echo "$app process is not running"
  fi
}
statusMysql(){
  pidlist=`ps -ef|grep mysql|grep my_plat|grep mysqld_safe|grep -v grep|awk '{print $2}'`
  pidcnt=`ps -ef|grep mysql|grep my_plat|grep mysqld_safe|grep -v grep|wc -l`
  if [[ $pidcnt -ge 1 ]];then
    echo "db process is running,pid:$pidlist"
  else
    echo "db process is not running"
  fi
}
#zip wrapper-all-0.0.2.RELEASE.jar BOOT-INF/classes/application.yml
######################## Main ########################################
if [[ $app == "db" ]];then
  if [[ $action == "start" ]];then
    startMysql
  elif [[ $action == "stop" ]];then
    stopMysql
  elif [[ $action == "restart" ]];then
    stopMysql
    sleep 2
    startMysql
  elif [[ $action == "status" ]];then
    statusMysql
  fi
  exit 0
fi


if [[ $action == "start" ]];then
  start
elif [[ $action == "stop" ]];then
  stop
elif [[ $action == "restart" ]];then
  restart
elif [[ $action == "status" ]];then
  status
else
  echo "Run Command Error.";
  help
  exit 1
fi
exit 0