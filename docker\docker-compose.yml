version: '3'
services:
#  app_dpanel:
#    image: registry.cn-hangzhou.aliyuncs.com/lank/app:fapp_dpanel
#    restart: always
#    hostname: app_dpanel
#    container_name: app_dpanel
#    ports:
#      - "30080:80"
#      - "30443:443"
#      - "38080:8080"
#    volumes:
#      - /var/run/docker.sock:/var/run/docker.sock
#      #- /var/run/containerd/containerd.sock:/var/run/docker.sock
#      - /app/docker/dpanel:/dpanel
#    environment:
#      APP_NAME: 'dpanel'
#      INSTALL_USERNAME: 'admin'
#      INSTALL_PASSWORD: 'P@ssw0rd123456'
  app_mysql:
    image: registry.cn-hangzhou.aliyuncs.com/lank/mysql:fapp_db_2.9.1.5
    restart: always
    hostname: app_mysql
    container_name: app_mysql
    ports:
      - "33306:3306"
    volumes:
      - /app/docker/mysql/log:/var/log
      - /app/docker/mysql/data:/var/lib/mysql
      - /app/docker/mysql/conf.d:/etc/mysql/conf.d
      - /etc/localtime:/etc/localtime:ro
    command:
      --max_connections=4096
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --lower_case_table_names=1
      --max_allowed_packet=100M
      --server-id=1
      --log-bin=mysql-bin
      --binlog_expire_logs_seconds=86400
      --binlog-format=ROW
      --binlog_cache_size=256m
      --slow_query_log=1
      --long_query_time=2
      --slow_query_log_file=/var/lib/mysql/dev_app_mysql-slow.log
      --innodb_buffer_pool_size=1G
    environment:
#      full,eam_sq,eam_qy,eam_qj,hr,
#      '["eam_sq"]'
      APP_MODE: '["eam_sq"]'
      MYSQL_ROOT_HOST: '%'
      MYSQL_ROOT_PASSWORD: 'P@ssw0rd123456'
      MYSQL_DATABASE: 'app'
      MYSQL_USER: 'app'
      MYSQL_PASSWORD: 'P@ssw0rd123456'
      TZ: 'Asia/Shanghai'
    privileged: true
    networks:
      app_docker_network:
        ipv4_address: ***********
  app_oss:
    image: registry.cn-hangzhou.aliyuncs.com/lank/minio:20240803
    restart: always
    hostname: app_oss
    container_name: app_oss
    ports:
      - "39000:9000"
      - "39090:9090"
    volumes:
      - /app/docker/oss/data:/data
      - /app/docker/oss/config:/root/.minio
    command: server /data --console-address ":9000" --address ":9090"
    environment:
      MINIO_ACCESS_KEY: 'admin'
      MINIO_SECRET_KEY: 'P@ssw0rd123456'
      MINIO_BROWSER: 'on'
    networks:
      app_docker_network:
        ipv4_address: ***********
  app_app:
    image: registry.cn-hangzhou.aliyuncs.com/lank/app:fapp_app_2.9.1.5
    restart: always
    hostname: app_app
    container_name: app_app
    ports:
      - "38089:8089"
    working_dir: /home
    volumes:
      - /app/docker/app/home:/home
      - /app/docker/app/netdisk:/app/app/app/netdisk
      - /app/docker/app/upload:/app/app/app/upload
    networks:
      app_docker_network:
        ipv4_address: ***********
    extra_hosts:
      - "db.app.dt:***********"
    environment:
      REDIS_HOST_IP: ***********
      BPM_HOST_IP: ***********
      MYSQL_HOST_IP: ***********
      MYSQL_HOST_PORT: 3306
      MYSQL_DATABASE: 'app'
      MYSQL_USER: 'app'
      MYSQL_PASSWORD: 'P@ssw0rd123456'
      STORAGE_TYPE: 'minio'
      MINIO_URL: 'http://***********:9090'
      MINIO_ACCESS_KEY: 'admin'
      MINIO_SECRET_KEY: 'P@ssw0rd123456'
      MINIO_ACCESS_KEY_YML: 'iLD9PcLSqv5Q0ELXpXQN'
      MINIO_SECRET_KEY_YML: 'nTdOWm5rW54ZpyZ6iCJovEedOwUq5uXe9W6H4kHW'
      BAIDU_MAP_WEB_AK: 'nTdOWm5rW54ZpyZ6iCJovEedOwUq5uXe9W6H4kHW'
      BAIDU_MAP_SERVICE_AK: 'nTdOWm5rW54ZpyZ6iCJovEedOwUq5uXe9W6H4kHW'
      TZ: 'Asia/Shanghai'
    depends_on:
      - app_mysql
      - app_oss
  app_bpm:
    image: registry.cn-hangzhou.aliyuncs.com/lank/app:fapp_bpm_2.9.1.5
    restart: always
    hostname: app_bpm
    container_name: app_bpm
    ports:
      - "8099:8099"
    working_dir: /home
    user: root
    privileged: true
    volumes:
      - /app/docker/bpm/home:/home
    networks:
      app_docker_network:
        ipv4_address: ***********
    extra_hosts:
      - "db.app.dt:***********"
    environment:
      APP_HOST_IP: ***********
      MYSQL_HOST_IP: ***********
      MYSQL_HOST_PORT: 3306
      MYSQL_DATABASE: 'app'
      MYSQL_USER: 'app'
      MYSQL_PASSWORD: 'P@ssw0rd123456'
      TZ: 'Asia/Shanghai'
    depends_on:
      - app_mysql
  app_redis:
    image: registry.cn-hangzhou.aliyuncs.com/lank/redis:fapp_redis_2.9.1.5
    restart: always
    hostname: app_redis
    container_name: app_redis
    ports:
      - "36379:6379"
    user: root
    privileged: true
    networks:
      app_docker_network:
        ipv4_address: ***********
  app_nginx:
    image: registry.cn-hangzhou.aliyuncs.com/lank/nginx:fapp_nginx_2.9.1.5
    restart: always
    hostname: app_nginx
    container_name: app_nginx
    ports:
      - "8089:8088"
      - "8088:8088"
      - "8090:8090"
      - "8091:8091"
      - "8899:8899"
    volumes:
      - /app/docker/nginx/log:/var/log/nginx
    user: root
    privileged: true
    environment:
      APP_NGINX: '应用'
    networks:
      app_docker_network:
        ipv4_address: ***********
  manager_tool:
    image: registry.cn-hangzhou.aliyuncs.com/lank/app:manager_tool_1
    container_name: manager_tool
    restart: always
    environment:
      MAYFLY_JWT_KEY: '53445c86e8189b6c646ed7d0d319015144423e72'
      MAYFLY_AES_KEY: '7bc5418eefd50402ef39107274891fbe'
      MAYFLY_DB_HOST: '***********:3306'
      MAYFLY_DB_USER: 'root'
      MAYFLY_DB_PASS: 'P@ssw0rd123456'
      MAYFLY_DB_NAME: 'mayfly_go'
      MAYFLY_WEB_USER: 'admin'
      MAYFLY_WEB_PASS: 'admin123.'
    ports:
      - "38888:18888"
    volumes:
      - /app/docker/mayfly-go/mayfly-go.log:/mayfly/mayfly-go.log
      - /app/docker/mayfly-go/rec:/mayfly/rec
    depends_on:
      - app_mysql
    networks:
      app_docker_network:
        ipv4_address: ***********
networks:
  app_docker_network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/24