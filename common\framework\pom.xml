<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dt.platform</groupId>
        <artifactId>parent-eam</artifactId>
        <version>*******</version>
        <relativePath>../../pom.xml</relativePath>
        <!-- lookup parent from repository -->
    </parent>
    <artifactId>framework-eam</artifactId>
    <name>framework-eam</name>
    <description>framework-platform</description>
    <packaging>jar</packaging>
    <dependencies>
        <dependency>
            <groupId>com.foxnicweb</groupId>
            <artifactId>foxnic-sql</artifactId>
            <version>${foxnic.version}</version>
        </dependency>
        <dependency>
            <groupId>com.foxnicweb</groupId>
            <artifactId>foxnic-dao</artifactId>
            <version>${foxnic.version}</version>
        </dependency>
        <dependency>
            <groupId>com.foxnicweb</groupId>
            <artifactId>foxnic-springboot</artifactId>
            <version>${foxnic.version}</version>
        </dependency>
        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>framework-boot</artifactId>
            <version>${foxnic-web.version}</version>
        </dependency>
        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>domain</artifactId>
            <version>${foxnic-web.version}</version>
        </dependency>
        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>proxy</artifactId>
            <version>${foxnic-web.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>domain-eam</artifactId>
            <version>${platform.version}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.poi/poi -->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.poi</groupId>-->
        <!--            <artifactId>poi</artifactId>-->
        <!--            <version>3.17</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.poi</groupId>-->
        <!--            <artifactId>poi-ooxml</artifactId>-->
        <!--            <version>3.17</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
            <version>3.0.2</version>
        </dependency>

        <!-- 添加Spring JDBC依赖 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>

        <!--        &lt;!&ndash; https://mvnrepository.com/artifact/org.apache.xmlbeans/xmlbeans &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.xmlbeans</groupId>-->
        <!--            <artifactId>xmlbeans</artifactId>-->
        <!--            <version>2.6.0</version>-->
        <!--        </dependency>-->
    </dependencies>
</project>
