#!/bin/sh
#################################################################################
#
#################################################################################
cur_dir=$(cd `dirname $0`; pwd)
cd $cur_dir
build_db=1
build_redis=1
build_nginx=1
build_nginx_h5=1
build_app=1
build_bpm=1
build_oss=1
#################################################################################

build_select=""
build_version="2.9.2"

if [[ -n $1 ]];then
  build_select=$1
  build_db=0
  build_redis=0
  build_nginx=0
  build_nginx_h5=0
  build_app=0
  build_bpm=0
  build_oss=0
else
  echo "sh build.sh all 2.9.2"
  echo "sh build.sh app"
  echo "sh build.sh setv 2.9.3"
  echo "sh build.sh push 2.9.3"
  echo "sh build.sh release 2.9.3"
  exit 1
fi

if [[ $build_select == "release" ]];then
  build_version=2.9.1.5
  if [[ -n $2 ]];then
    build_version=$2
  fi
  echo "build_version:$build_version"
  #docker pull registry.cn-hangzhou.aliyuncs.com/lank/mysql:fapp_db_$build_version
  #docker pull registry.cn-hangzhou.aliyuncs.com/lank/app:fapp_app_$build_version
  #docker pull registry.cn-hangzhou.aliyuncs.com/lank/app:fapp_bpm_$build_version
  #docker pull registry.cn-hangzhou.aliyuncs.com/lank/nginx:fapp_nginx_$build_version
  #docker pull registry.cn-hangzhou.aliyuncs.com/lank/redis:fapp_redis_$build_version
  #docker pull registry.cn-hangzhou.aliyuncs.com/lank/minio:20240803
  #docker pull registry.cn-hangzhou.aliyuncs.com/lank/app:manager_tool_1
  docker save -o image-app-db.tar.gz     registry.cn-hangzhou.aliyuncs.com/lank/mysql:fapp_db_$build_version
  docker save -o image-app-app.tar.gz    registry.cn-hangzhou.aliyuncs.com/lank/app:fapp_app_$build_version
  docker save -o image-app-bpm.tar.gz    registry.cn-hangzhou.aliyuncs.com/lank/app:fapp_bpm_$build_version
  docker save -o image-app-nginx.tar.gz  registry.cn-hangzhou.aliyuncs.com/lank/nginx:fapp_nginx_$build_version
  docker save -o image-app-redis.tar.gz  registry.cn-hangzhou.aliyuncs.com/lank/redis:fapp_redis_$build_version
  docker save -o image-app-h5.tar.gz  registry.cn-hangzhou.aliyuncs.com/lank/nginx:fapp_h5_$build_version
  docker save -o image-app-minio.tar.gz  registry.cn-hangzhou.aliyuncs.com/lank/minio:20240803
  docker save -o image-app-tool.tar.gz   registry.cn-hangzhou.aliyuncs.com/lank/app:manager_tool_1
  docker save -o image-app-dpanel.tar.gz registry.cn-hangzhou.aliyuncs.com/lank/app:fapp_dpanel
  rm -rf /opt/website/release/image-*.tar.gz
  mv image-*.gz /opt/website/release/
  chmod 755 /opt/website/release/image*.gz
  echo "/opt/website/release image list"
  ls -rtl /opt/website/release/ |grep image|grep tar
  exit 0
fi

if [[ $build_select == "help" ]];then
  echo "sh build.sh all 2.9.2"
  echo "sh build.sh app"
  echo "sh build.sh setv 2.9.3"
  echo "sh build.sh push 2.9.3"
  echo "sh build.sh release 2.9.3"
  exit 0
fi

if [[ $build_select == "db" ]];then
  build_db=1
fi
if [[ $build_select == "redis" ]];then
  build_redis=1
fi
if [[ $build_select == "nginx" ]];then
  build_nginx=1
fi
if [[ $build_select == "nginx_h5" ]];then
  build_nginx_h5=1
fi
if [[ $build_select == "app" ]];then
  build_app=1
fi
if [[ $build_select == "bpm" ]];then
  build_bpm=1
fi
if [[ $build_select == "all" ]];then
  build_db=1
  build_redis=1
  build_nginx=1
  build_nginx_h5=1
  build_app=1
  build_bpm=1
fi
if [[ $build_select == "push" ]];then
   docker images|grep $2|grep lank|awk '{print "docker push " $1  ":" $2}'
   exit 0
fi

if [[ $build_select == "setv" ]];then
  curV=`cat docker-compose.yml |grep fapp|head -1|awk -F ':' '{print $NF}'|awk -F '_' '{print $NF}'`
  newV=`echo $curV`
  if [[ -n $2 ]];then
    newV=$2
  fi
  echo "curV:$curV,newV:$newV"
  sed -i "s/$curV/$newV/g" docker-compose.yml

  curV=`cat docker-compose.yml |grep fapp|head -1|awk -F ':' '{print $NF}'|awk -F '_' '{print $NF}'`
  newV=`echo $curV`
  if [[ -n $2 ]];then
    newV=$2
  fi
  echo "curV:$curV,newV:$newV"
  sed -i "s/$curV/$newV/g" docker-compose.yml
  exit 1
fi



if [[ -n $2 ]];then
  build_version=$2
fi
echo "build verson:$build_version"
echo "build_db:$build_db,build_redis:$build_redis,build_nginx_h5:$build_nginx_h5,build_nginx:$build_nginx,build_app:$build_app,build_bpm:$build_bpm"

if [[ $build_db -eq 1 ]];then
  echo "build db...."
  cd $cur_dir
  sh build_db.sh $build_version
fi

if [[ $build_nginx -eq 1 ]];then
  echo "build nginx...."
  cd $cur_dir
  cat build_nginx.sh>$cur_dir/../tengine/build_nginx.sh
  cd $cur_dir/../tengine/
  sh build_nginx.sh $build_version
fi

if [[ $build_nginx_h5 -eq 1 ]];then
  echo "build nginx h5 ...."
  cd $cur_dir
  cat build_nginx_h5.sh>$cur_dir/../tengine/build_nginx_h5.sh
  cd $cur_dir/../tengine/
  sh build_nginx_h5.sh $build_version
fi

if [[ $build_app -eq 1 ]];then
  echo "build app...."
  cd $cur_dir
  cat build_app.sh>$cur_dir/../app/build_app.sh
  cd $cur_dir/../app/
  sh build_app.sh $build_version
fi

if [[ $build_bpm -eq 1 ]];then
  echo "build bpm...."
  cd $cur_dir
  cat build_bpm.sh>$cur_dir/../bpm/build_bpm.sh
  cd $cur_dir/../bpm/
  sh build_bpm.sh $build_version
fi

if [[ $build_redis -eq 1 ]];then
  echo "build redis...."
  cd $cur_dir
  sh build_redis.sh $build_version
fi

exit 0

docker run -it -d --name dpanel --restart=always  -p 30080:80 -p 30443:443 -p 38080:8080 -e APP_NAME=dpanel  -v /var/run/docker.sock:/var/run/docker.sock -v dpanel:/dpanel  -e INSTALL_USERNAME=admin -e INSTALL_PASSWORD=admin registry.cn-hangzhou.aliyuncs.com/dpanel/dpanel:latest
###################################################################################################################################

################################ IP 列表 ###################################
***********  app_nginx_h5
***********  app_nginx
***********  app_bpm
***********  app_app
***********  app_db
***********  app_redis
***********  app_oss
***********  manager_tool
################################ 删除 ######################################
docker images|grep fapp_|awk '{print "docker rmi " $3}'
################################ 进入容器 ###################################
docker exec -it app_app /bin/bash

############################ push image ###################################
docker push registry.cn-hangzhou.aliyuncs.com/lank/app:fapp_app_2.9.1.5
docker push registry.cn-hangzhou.aliyuncs.com/lank/app:fapp_bpm_2.9.1.5
docker push registry.cn-hangzhou.aliyuncs.com/lank/redis:fapp_redis_2.9.1.5
docker push registry.cn-hangzhou.aliyuncs.com/lank/mysql:fapp_db_2.9.1.5
docker push registry.cn-hangzhou.aliyuncs.com/lank/nginx:fapp_nginx_2.9.1.5
docker push registry.cn-hangzhou.aliyuncs.com/lank/nginx:fapp_h5_2.9.1.5

docker push registry.cn-hangzhou.aliyuncs.com/lank/minio:20240803

############################ 全部清理,数据也会清理 ############################
docker stop app_bpm
docker rm app_bpm
docker stop app_app
docker rm app_app
docker stop app_nginx
docker rm app_nginx
docker stop app_redis
docker rm app_redis
docker stop app_minio
docker rm app_minio
docker stop app_mysql
docker rm app_mysql
docker stop app_oss
docker rm app_oss
rm -rf /app/docker/

echo "clear finish"

#启动
docker-compose up -d

##配置
sudo tee /etc/docker/daemon.json <<EOF
{
    "registry-mirrors": [
        "https://hub.uuuadc.top",
        "https://docker.anyhub.us.kg",
        "https://dockerhub.jobcher.com",
        "https://dockerhub.icu",
        "https://docker.ckyl.me",
        "https://docker.awsl9527.cn"
    ]
}
EOF
sudo systemctl daemon-reload
sudo systemctl restart docker