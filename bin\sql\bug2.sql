alter table app_module_group modify create_by varchar(24);
alter table app_module_group modify update_by varchar(24);
alter table app_module_group modify delete_by varchar(24);
alter table app_module_group modify tenant_id varchar(24);
alter table app_module_info modify create_by varchar(24);
alter table app_module_info modify update_by varchar(24);
alter table app_module_info modify delete_by varchar(24);
alter table app_module_info modify tenant_id varchar(24);
alter table app_software_group modify create_by varchar(24);
alter table app_software_group modify update_by varchar(24);
alter table app_software_group modify delete_by varchar(24);
alter table app_software_group modify tenant_id varchar(24);
alter table app_software_info modify create_by varchar(24);
alter table app_software_info modify update_by varchar(24);
alter table app_software_info modify delete_by varchar(24);
alter table app_software_info modify tenant_id varchar(24);
alter table bpm_catalog modify id varchar(24);
alter table bpm_catalog modify icon_file_pc varchar(24);
alter table bpm_catalog modify icon_file_mobile varchar(24);
alter table bpm_catalog modify create_by varchar(24);
alter table bpm_catalog modify update_by varchar(24);
alter table bpm_catalog modify delete_by varchar(24);
alter table bpm_catalog modify tenant_id varchar(24);
alter table bpm_demo_business_case modify id varchar(24);
alter table bpm_demo_common modify id varchar(24);
alter table bpm_demo_leave modify id varchar(24);
alter table bpm_demo_leave modify applicant_id varchar(24);
alter table bpm_form_definition modify id varchar(24);
alter table bpm_form_definition modify create_by varchar(24);
alter table bpm_form_definition modify update_by varchar(24);
alter table bpm_form_definition modify delete_by varchar(24);
alter table bpm_form_definition modify tenant_id varchar(24);
alter table bpm_form_definition modify catalog_id varchar(24);
alter table bpm_form_instance modify id varchar(24);
alter table bpm_form_instance modify process_definition_id varchar(24);
alter table bpm_form_instance modify process_instance_id varchar(24);
alter table bpm_form_instance modify form_definition_id varchar(24);
alter table bpm_form_instance modify create_by varchar(24);
alter table bpm_form_instance modify update_by varchar(24);
alter table bpm_form_instance modify delete_by varchar(24);
alter table bpm_form_instance modify tenant_id varchar(24);
alter table bpm_form_instance_bill modify id varchar(24);
alter table bpm_form_instance_bill modify form_instance_id varchar(24);
alter table bpm_form_instance_bill modify create_by varchar(24);
alter table bpm_form_instance_bill modify update_by varchar(24);
alter table bpm_form_instance_bill modify delete_by varchar(24);
alter table bpm_form_instance_bill modify tenant_id varchar(24);
alter table bpm_process_definition modify id varchar(24);
alter table bpm_process_definition modify form_definition_id varchar(24);
alter table bpm_process_definition modify create_by varchar(24);
alter table bpm_process_definition modify update_by varchar(24);
alter table bpm_process_definition modify delete_by varchar(24);
alter table bpm_process_definition modify tenant_id varchar(24);
alter table bpm_process_definition modify icon_file_pc varchar(24);
alter table bpm_process_definition modify icon_file_mobile varchar(24);
alter table bpm_process_definition modify catalog_id varchar(24);
alter table bpm_process_definition_deploy modify id varchar(24);
alter table bpm_process_definition_deploy modify process_definition_id varchar(24);
alter table bpm_process_definition_deploy modify file_id varchar(24);
alter table bpm_process_definition_deploy modify tenant_id varchar(24);
alter table bpm_process_definition_file modify id varchar(24);
alter table bpm_process_definition_file modify process_definition_id varchar(24);
alter table bpm_process_definition_file modify file_id varchar(24);
alter table bpm_process_definition_file modify create_by varchar(24);
alter table bpm_process_definition_file modify update_by varchar(24);
alter table bpm_process_definition_file modify delete_by varchar(24);
alter table bpm_process_definition_file modify tenant_id varchar(24);
alter table bpm_process_definition_node modify id varchar(24);
alter table bpm_process_definition_node modify process_definition_id varchar(24);
alter table bpm_process_definition_node modify process_definition_file_id varchar(24);
alter table bpm_process_definition_node modify create_by varchar(24);
alter table bpm_process_definition_node modify update_by varchar(24);
alter table bpm_process_definition_node modify delete_by varchar(24);
alter table bpm_process_definition_node modify tenant_id varchar(24);
alter table bpm_process_definition_node_assignee modify id varchar(24);
alter table bpm_process_definition_node_assignee modify node_id varchar(24);
alter table bpm_process_definition_node_assignee modify assignee_type varchar(24);
alter table bpm_process_definition_node_assignee modify assignee_id varchar(24);
alter table bpm_process_definition_node_assignee modify create_by varchar(24);
alter table bpm_process_definition_node_assignee modify update_by varchar(24);
alter table bpm_process_definition_node_assignee modify delete_by varchar(24);
alter table bpm_process_definition_node_assignee modify tenant_id varchar(24);
alter table bpm_process_error modify id varchar(24);
alter table bpm_process_error modify process_instance_id varchar(24);
alter table bpm_process_initiator modify id varchar(24);
alter table bpm_process_initiator modify definition_id varchar(24);
alter table bpm_process_initiator modify initiator_id varchar(24);
alter table bpm_process_initiator modify create_by varchar(24);
alter table bpm_process_initiator modify update_by varchar(24);
alter table bpm_process_initiator modify delete_by varchar(24);
alter table bpm_process_initiator modify tenant_id varchar(24);
alter table bpm_process_instance modify id varchar(24);
alter table bpm_process_instance modify drafter_user_id varchar(24);
alter table bpm_process_instance modify drafter_id varchar(24);
alter table bpm_process_instance modify form_instance_id varchar(24);
alter table bpm_process_instance modify process_definition_id varchar(24);
alter table bpm_process_instance modify process_definition_file_id varchar(24);
alter table bpm_process_instance modify form_definition_id varchar(24);
alter table bpm_process_instance modify abandon_user_id varchar(24);
alter table bpm_process_instance modify create_by varchar(24);
alter table bpm_process_instance modify update_by varchar(24);
alter table bpm_process_instance modify delete_by varchar(24);
alter table bpm_process_instance modify tenant_id varchar(24);
alter table bpm_process_instance_dynamic_assignee modify id varchar(24);
alter table bpm_process_instance_dynamic_assignee modify process_instance_id varchar(24);
alter table bpm_process_instance_dynamic_assignee modify create_by varchar(24);
alter table bpm_process_instance_dynamic_assignee modify update_by varchar(24);
alter table bpm_process_instance_dynamic_assignee modify delete_by varchar(24);
alter table bpm_process_instance_dynamic_assignee modify tenant_id varchar(24);
alter table bpm_process_instance_remind modify id varchar(24);
alter table bpm_process_instance_remind modify process_instance_id varchar(24);
alter table bpm_process_instance_remind modify target_node_id varchar(24);
alter table bpm_process_instance_remind modify source_node_id varchar(24);
alter table bpm_process_instance_remind modify tenant_id varchar(24);
alter table bpm_process_instance_remind modify create_by varchar(24);
alter table bpm_process_instance_remind modify update_by varchar(24);
alter table bpm_process_instance_remind modify delete_by varchar(24);
alter table bpm_process_instance_remind_receiver modify id varchar(24);
alter table bpm_process_instance_remind_receiver modify remind_id varchar(24);
alter table bpm_process_instance_remind_receiver modify receiver_id varchar(24);
alter table bpm_process_instance_remind_receiver modify tenant_id varchar(24);
alter table bpm_process_instance_remind_receiver modify create_by varchar(24);
alter table bpm_process_instance_remind_receiver modify update_by varchar(24);
alter table bpm_process_instance_remind_receiver modify delete_by varchar(24);
alter table bpm_task modify id varchar(24);
alter table bpm_task modify process_definition_id varchar(24);
alter table bpm_task modify process_instance_id varchar(24);
alter table bpm_task modify create_by varchar(24);
alter table bpm_task modify update_by varchar(24);
alter table bpm_task modify delete_by varchar(24);
alter table bpm_task modify tenant_id varchar(24);
alter table bpm_task_approval modify id varchar(24);
alter table bpm_task_approval modify task_id varchar(24);
alter table bpm_task_approval modify process_instance_id varchar(24);
alter table bpm_task_approval modify approval_user_id varchar(24);
alter table bpm_task_approval modify assignee_type varchar(24);
alter table bpm_task_approval modify assignee_id varchar(24);
alter table bpm_task_approval modify create_by varchar(24);
alter table bpm_task_approval modify update_by varchar(24);
alter table bpm_task_approval modify delete_by varchar(24);
alter table bpm_task_approval modify tenant_id varchar(24);
alter table bpm_task_approval_attachment modify id varchar(24);
alter table bpm_task_approval_attachment modify approval_id varchar(24);
alter table bpm_task_approval_attachment modify process_instance_id varchar(24);
alter table bpm_task_approval_attachment modify file_id varchar(24);
alter table bpm_task_approval_attachment modify create_by varchar(24);
alter table bpm_task_approval_attachment modify update_by varchar(24);
alter table bpm_task_approval_attachment modify delete_by varchar(24);
alter table bpm_task_approval_attachment modify tenant_id varchar(24);
alter table bpm_task_assignee modify id varchar(24);
alter table bpm_task_assignee modify process_instance_id varchar(24);
alter table bpm_task_assignee modify task_id varchar(24);
alter table bpm_task_assignee modify assignee_type varchar(24);
alter table bpm_task_assignee modify assignee_id varchar(24);
alter table bpm_task_assignee modify create_by varchar(24);
alter table bpm_task_assignee modify update_by varchar(24);
alter table bpm_task_assignee modify delete_by varchar(24);
alter table bpm_task_assignee modify tenant_id varchar(24);
alter table bpm_task_read modify id varchar(24);
alter table bpm_task_read modify process_instance_id varchar(24);
alter table bpm_task_read modify task_id varchar(24);
alter table bpm_task_read modify create_by varchar(24);
alter table bpm_task_read modify tenant_id varchar(24);
alter table bpm_user_statistics modify id varchar(24);
alter table bpm_user_statistics modify process_definition_id varchar(24);
alter table bpm_user_statistics modify create_by varchar(24);
alter table bpm_user_statistics modify update_by varchar(24);
alter table bpm_user_statistics modify delete_by varchar(24);
alter table bpm_user_statistics modify tenant_id varchar(24);
alter table chs_change_approver modify id varchar(24);
alter table chs_change_approver modify definition_id varchar(24);
alter table chs_change_approver modify instance_id varchar(24);
alter table chs_change_approver modify approver_id varchar(24);
alter table chs_change_approver modify node_id varchar(24);
alter table chs_change_approver modify create_by varchar(24);
alter table chs_change_approver modify update_by varchar(24);
alter table chs_change_approver modify delete_by varchar(24);
alter table chs_change_bill modify id varchar(24);
alter table chs_change_bill modify definition_id varchar(24);
alter table chs_change_bill modify instance_id varchar(24);
alter table chs_change_bill modify bill_id varchar(24);
alter table chs_change_bill modify create_by varchar(24);
alter table chs_change_bill modify update_by varchar(24);
alter table chs_change_bill modify delete_by varchar(24);
alter table chs_change_data modify id varchar(24);
alter table chs_change_data modify instance_id varchar(24);
alter table chs_change_data modify create_by varchar(24);
alter table chs_change_data modify update_by varchar(24);
alter table chs_change_data modify delete_by varchar(24);
alter table chs_change_definition modify id varchar(24);
alter table chs_change_definition modify mode varchar(24);
alter table chs_change_definition modify create_by varchar(24);
alter table chs_change_definition modify update_by varchar(24);
alter table chs_change_definition modify delete_by varchar(24);
alter table chs_change_event modify id varchar(24);
alter table chs_change_event modify instance_id varchar(24);
alter table chs_change_event modify approver_id varchar(24);
alter table chs_change_event modify approver_name varchar(24);
alter table chs_change_event modify create_by varchar(24);
alter table chs_change_event modify update_by varchar(24);
alter table chs_change_event modify delete_by varchar(24);
alter table chs_change_event modify simple_node_id varchar(24);
alter table chs_change_instance modify id varchar(24);
alter table chs_change_instance modify definition_id varchar(24);
alter table chs_change_instance modify drafter_id varchar(24);
alter table chs_change_instance modify drafter_name varchar(24);
alter table chs_change_instance modify tenant_id varchar(24);
alter table chs_change_instance modify create_by varchar(24);
alter table chs_change_instance modify update_by varchar(24);
alter table chs_change_instance modify delete_by varchar(24);
alter table chs_change_instance modify process_id varchar(24);
alter table chs_change_instance modify simple_node_id varchar(24);
alter table chs_example_order modify id varchar(24);
alter table chs_example_order modify buyer_id varchar(24);
alter table chs_example_order modify tenant_id varchar(24);
alter table chs_example_order modify create_by varchar(24);
alter table chs_example_order modify update_by varchar(24);
alter table chs_example_order modify delete_by varchar(24);
alter table chs_example_order modify source_id varchar(24);
alter table chs_example_order modify change_instance_id varchar(24);
alter table chs_example_order modify latest_approver_id varchar(24);
alter table chs_example_order modify latest_approver_name varchar(24);
alter table chs_example_order_item modify id varchar(24);
alter table chs_example_order_item modify order_id varchar(24);
alter table chs_example_order_item modify tenant_id varchar(24);
alter table chs_example_order_item modify create_by varchar(24);
alter table chs_example_order_item modify update_by varchar(24);
alter table chs_example_order_item modify delete_by varchar(24);
alter table cont_contract modify id varchar(24);
alter table cont_contract modify parent_id varchar(24);
alter table cont_contract modify tenant_id varchar(24);
alter table cont_contract modify create_by varchar(24);
alter table cont_contract modify update_by varchar(24);
alter table cont_contract modify delete_by varchar(24);
alter table cont_contract_accept modify id varchar(24);
alter table cont_contract_accept modify belong_id varchar(24);
alter table cont_contract_accept modify tenant_id varchar(24);
alter table cont_contract_accept modify create_by varchar(24);
alter table cont_contract_accept modify update_by varchar(24);
alter table cont_contract_accept modify delete_by varchar(24);
alter table cont_contract_archive modify id varchar(24);
alter table cont_contract_archive modify tenant_id varchar(24);
alter table cont_contract_archive modify create_by varchar(24);
alter table cont_contract_archive modify update_by varchar(24);
alter table cont_contract_archive modify delete_by varchar(24);
alter table cont_contract_attachment modify id varchar(24);
alter table cont_contract_attachment modify owner_id varchar(24);
alter table cont_contract_attachment modify file_id varchar(24);
alter table cont_contract_attachment modify tenant_id varchar(24);
alter table cont_contract_attachment modify create_by varchar(24);
alter table cont_contract_attachment modify update_by varchar(24);
alter table cont_contract_attachment modify delete_by varchar(24);
alter table cont_contract_category modify create_by varchar(24);
alter table cont_contract_category modify update_by varchar(24);
alter table cont_contract_category modify delete_by varchar(24);
alter table cont_contract_category modify tenant_id varchar(24);
alter table cont_contract_change modify id varchar(24);
alter table cont_contract_change modify tenant_id varchar(24);
alter table cont_contract_change modify create_by varchar(24);
alter table cont_contract_change modify update_by varchar(24);
alter table cont_contract_change modify delete_by varchar(24);
alter table cont_contract_collect modify id varchar(24);
alter table cont_contract_collect modify tenant_id varchar(24);
alter table cont_contract_collect modify create_by varchar(24);
alter table cont_contract_collect modify update_by varchar(24);
alter table cont_contract_collect modify delete_by varchar(24);
alter table cont_contract_counterparty modify id varchar(24);
alter table cont_contract_counterparty modify tenant_id varchar(24);
alter table cont_contract_counterparty modify create_by varchar(24);
alter table cont_contract_counterparty modify update_by varchar(24);
alter table cont_contract_counterparty modify delete_by varchar(24);
alter table cont_contract_event modify id varchar(24);
alter table cont_contract_event modify tenant_id varchar(24);
alter table cont_contract_event modify create_by varchar(24);
alter table cont_contract_event modify update_by varchar(24);
alter table cont_contract_event modify delete_by varchar(24);
alter table cont_contract_internal_body modify id varchar(24);
alter table cont_contract_internal_body modify tenant_id varchar(24);
alter table cont_contract_internal_body modify create_by varchar(24);
alter table cont_contract_internal_body modify update_by varchar(24);
alter table cont_contract_internal_body modify delete_by varchar(24);
alter table cont_contract_invoice modify id varchar(24);
alter table cont_contract_invoice modify file_id varchar(24);
alter table cont_contract_invoice modify create_by varchar(24);
alter table cont_contract_invoice modify update_by varchar(24);
alter table cont_contract_invoice modify delete_by varchar(24);
alter table cont_contract_label modify create_by varchar(24);
alter table cont_contract_label modify update_by varchar(24);
alter table cont_contract_label modify delete_by varchar(24);
alter table cont_contract_label modify tenant_id varchar(24);
alter table cont_contract_label_s modify create_by varchar(24);
alter table cont_contract_label_s modify update_by varchar(24);
alter table cont_contract_label_s modify delete_by varchar(24);
alter table cont_contract_label_s modify tenant_id varchar(24);
alter table cont_contract_pay modify id varchar(24);
alter table cont_contract_pay modify tenant_id varchar(24);
alter table cont_contract_pay modify create_by varchar(24);
alter table cont_contract_pay modify update_by varchar(24);
alter table cont_contract_pay modify delete_by varchar(24);
alter table cont_contract_payment_label modify create_by varchar(24);
alter table cont_contract_payment_label modify update_by varchar(24);
alter table cont_contract_payment_label modify delete_by varchar(24);
alter table cont_contract_payment_label modify tenant_id varchar(24);
alter table cont_contract_performance modify id varchar(24);
alter table cont_contract_performance modify contract_id varchar(24);
alter table cont_contract_performance modify create_by varchar(24);
alter table cont_contract_performance modify update_by varchar(24);
alter table cont_contract_performance modify delete_by varchar(24);
alter table cont_contract_sign_item modify id varchar(24);
alter table cont_contract_sign_item modify tenant_id varchar(24);
alter table cont_contract_sign_item modify create_by varchar(24);
alter table cont_contract_sign_item modify update_by varchar(24);
alter table cont_contract_sign_item modify delete_by varchar(24);
alter table cont_contract_signer modify id varchar(24);
alter table cont_contract_signer modify contract_id varchar(24);
alter table cont_contract_signer modify tenant_id varchar(24);
alter table cont_contract_signer modify create_by varchar(24);
alter table cont_contract_signer modify update_by varchar(24);
alter table cont_contract_signer modify delete_by varchar(24);
alter table cont_contract_signer_type modify id varchar(24);
alter table cont_contract_signer_type modify tenant_id varchar(24);
alter table cont_contract_signer_type modify create_by varchar(24);
alter table cont_contract_signer_type modify update_by varchar(24);
alter table cont_contract_signer_type modify delete_by varchar(24);
alter table cont_invoice_out modify id varchar(24);
alter table cont_invoice_out modify owner_id varchar(24);
alter table cont_invoice_out modify org_id varchar(24);
alter table cont_invoice_out modify tenant_id varchar(24);
alter table cont_invoice_out modify create_by varchar(24);
alter table cont_invoice_out modify update_by varchar(24);
alter table cont_invoice_out modify delete_by varchar(24);
alter table cont_invoice_receive modify id varchar(24);
alter table cont_invoice_receive modify owner_id varchar(24);
alter table cont_invoice_receive modify org_id varchar(24);
alter table cont_invoice_receive modify tenant_id varchar(24);
alter table cont_invoice_receive modify create_by varchar(24);
alter table cont_invoice_receive modify update_by varchar(24);
alter table cont_invoice_receive modify delete_by varchar(24);
alter table cont_partner_black modify id varchar(24);
alter table cont_partner_black modify tenant_id varchar(24);
alter table cont_partner_black modify create_by varchar(24);
alter table cont_partner_black modify update_by varchar(24);
alter table cont_partner_black modify delete_by varchar(24);
alter table cst1_c1_report modify create_by varchar(24);
alter table cst1_c1_report modify update_by varchar(24);
alter table cst1_c1_report modify delete_by varchar(24);
alter table cst_asset_bill modify create_by varchar(24);
alter table cst_asset_bill modify update_by varchar(24);
alter table cst_asset_bill modify delete_by varchar(24);
alter table cst_asset_bill_policy modify create_by varchar(24);
alter table cst_asset_bill_policy modify update_by varchar(24);
alter table cst_asset_bill_policy modify delete_by varchar(24);
alter table cst_asset_info modify create_by varchar(24);
alter table cst_asset_info modify update_by varchar(24);
alter table cst_asset_info modify delete_by varchar(24);
alter table cst_contract modify create_by varchar(24);
alter table cst_contract modify update_by varchar(24);
alter table cst_contract modify delete_by varchar(24);
alter table cst_cq_qualification modify create_by varchar(24);
alter table cst_cq_qualification modify update_by varchar(24);
alter table cst_cq_qualification modify delete_by varchar(24);
alter table cst_customer_info modify create_by varchar(24);
alter table cst_customer_info modify update_by varchar(24);
alter table cst_customer_info modify delete_by varchar(24);
alter table cst_lease_discount modify create_by varchar(24);
alter table cst_lease_discount modify update_by varchar(24);
alter table cst_lease_discount modify delete_by varchar(24);
alter table cst_report modify create_by varchar(24);
alter table cst_report modify update_by varchar(24);
alter table cst_report modify delete_by varchar(24);
alter table cst_td_qualification modify create_by varchar(24);
alter table cst_td_qualification modify update_by varchar(24);
alter table cst_td_qualification modify delete_by varchar(24);
alter table cst_yyzz_qualification modify create_by varchar(24);
alter table cst_yyzz_qualification modify update_by varchar(24);
alter table cst_yyzz_qualification modify delete_by varchar(24);
alter table cust_clps_demo modify create_by varchar(24);
alter table cust_clps_demo modify update_by varchar(24);
alter table cust_clps_demo modify delete_by varchar(24);
alter table cust_clps_report modify create_by varchar(24);
alter table cust_clps_report modify update_by varchar(24);
alter table cust_clps_report modify delete_by varchar(24);
alter table cust_repiar_demo modify create_by varchar(24);
alter table cust_repiar_demo modify update_by varchar(24);
alter table cust_repiar_demo modify delete_by varchar(24);
alter table cust_test modify create_by varchar(24);
alter table cust_test modify update_by varchar(24);
alter table cust_test modify delete_by varchar(24);
alter table cust_test modify tenant_id varchar(24);
alter table dc_area modify create_by varchar(24);
alter table dc_area modify update_by varchar(24);
alter table dc_area modify delete_by varchar(24);
alter table dc_area modify tenant_id varchar(24);
alter table dc_info modify create_by varchar(24);
alter table dc_info modify update_by varchar(24);
alter table dc_info modify delete_by varchar(24);
alter table dc_info modify tenant_id varchar(24);
alter table dc_layer modify create_by varchar(24);
alter table dc_layer modify update_by varchar(24);
alter table dc_layer modify delete_by varchar(24);
alter table dc_layer modify tenant_id varchar(24);
alter table dc_rack modify create_by varchar(24);
alter table dc_rack modify update_by varchar(24);
alter table dc_rack modify delete_by varchar(24);
alter table dc_rack modify tenant_id varchar(24);
alter table dp_example_brand modify id varchar(24);
alter table dp_example_brand modify create_by varchar(24);
alter table dp_example_brand modify update_by varchar(24);
alter table dp_example_brand modify delete_by varchar(24);
alter table dp_example_catalog modify id varchar(24);
alter table dp_example_catalog modify parent_id varchar(24);
alter table dp_example_catalog modify create_by varchar(24);
alter table dp_example_catalog modify update_by varchar(24);
alter table dp_example_catalog modify delete_by varchar(24);
alter table dp_example_order modify id varchar(24);
alter table dp_example_order modify product_id varchar(24);
alter table dp_example_order modify shop_id varchar(24);
alter table dp_example_order modify sales_id varchar(24);
alter table dp_example_order modify create_by varchar(24);
alter table dp_example_order modify update_by varchar(24);
alter table dp_example_order modify delete_by varchar(24);
alter table dp_example_product modify id varchar(24);
alter table dp_example_product modify create_by varchar(24);
alter table dp_example_product modify update_by varchar(24);
alter table dp_example_product modify delete_by varchar(24);
alter table dp_example_shop modify id varchar(24);
alter table dp_example_shop modify org_id varchar(24);
alter table dp_example_shop modify create_by varchar(24);
alter table dp_example_shop modify update_by varchar(24);
alter table dp_example_shop modify delete_by varchar(24);
alter table dp_rule modify id varchar(24);
alter table dp_rule modify create_by varchar(24);
alter table dp_rule modify update_by varchar(24);
alter table dp_rule modify delete_by varchar(24);
alter table dp_rule_condition modify id varchar(24);
alter table dp_rule_condition modify rule_id varchar(24);
alter table dp_rule_condition modify range_id varchar(24);
alter table dp_rule_condition modify parent_id varchar(24);
alter table dp_rule_condition modify create_by varchar(24);
alter table dp_rule_condition modify update_by varchar(24);
alter table dp_rule_condition modify delete_by varchar(24);
alter table dp_rule_range modify id varchar(24);
alter table dp_rule_range modify rule_id varchar(24);
alter table dp_rule_range modify create_by varchar(24);
alter table dp_rule_range modify update_by varchar(24);
alter table dp_rule_range modify delete_by varchar(24);
alter table eam_action_crontab modify create_by varchar(24);
alter table eam_action_crontab modify update_by varchar(24);
alter table eam_action_crontab modify delete_by varchar(24);
alter table eam_action_crontab modify tenant_id varchar(24);
alter table eam_action_crontab_log modify create_by varchar(24);
alter table eam_action_crontab_log modify update_by varchar(24);
alter table eam_action_crontab_log modify delete_by varchar(24);
alter table eam_approve_configure modify create_by varchar(24);
alter table eam_approve_configure modify update_by varchar(24);
alter table eam_approve_configure modify delete_by varchar(24);
alter table eam_approve_role modify create_by varchar(24);
alter table eam_approve_role modify update_by varchar(24);
alter table eam_approve_role modify delete_by varchar(24);
alter table eam_asset modify create_by varchar(24);
alter table eam_asset modify update_by varchar(24);
alter table eam_asset modify delete_by varchar(24);
alter table eam_asset modify tenant_id varchar(24);
alter table eam_asset_allocation modify create_by varchar(24);
alter table eam_asset_allocation modify update_by varchar(24);
alter table eam_asset_allocation modify delete_by varchar(24);
alter table eam_asset_allocation modify tenant_id varchar(24);
alter table eam_asset_attribute modify create_by varchar(24);
alter table eam_asset_attribute modify update_by varchar(24);
alter table eam_asset_attribute modify delete_by varchar(24);
alter table eam_asset_attribute modify tenant_id varchar(24);
alter table eam_asset_attribute_item modify create_by varchar(24);
alter table eam_asset_attribute_item modify update_by varchar(24);
alter table eam_asset_attribute_item modify delete_by varchar(24);
alter table eam_asset_attribute_item modify tenant_id varchar(24);
alter table eam_asset_batch modify create_by varchar(24);
alter table eam_asset_batch modify update_by varchar(24);
alter table eam_asset_batch modify delete_by varchar(24);
alter table eam_asset_borrow modify create_by varchar(24);
alter table eam_asset_borrow modify update_by varchar(24);
alter table eam_asset_borrow modify delete_by varchar(24);
alter table eam_asset_borrow modify tenant_id varchar(24);
alter table eam_asset_borrow_return modify create_by varchar(24);
alter table eam_asset_borrow_return modify update_by varchar(24);
alter table eam_asset_borrow_return modify delete_by varchar(24);
alter table eam_asset_borrow_return modify tenant_id varchar(24);
alter table eam_asset_collection modify create_by varchar(24);
alter table eam_asset_collection modify update_by varchar(24);
alter table eam_asset_collection modify delete_by varchar(24);
alter table eam_asset_collection modify tenant_id varchar(24);
alter table eam_asset_collection_return modify create_by varchar(24);
alter table eam_asset_collection_return modify update_by varchar(24);
alter table eam_asset_collection_return modify delete_by varchar(24);
alter table eam_asset_collection_return modify tenant_id varchar(24);
alter table eam_asset_copy modify create_by varchar(24);
alter table eam_asset_copy modify update_by varchar(24);
alter table eam_asset_copy modify delete_by varchar(24);
alter table eam_asset_copy modify tenant_id varchar(24);
alter table eam_asset_copy_record modify create_by varchar(24);
alter table eam_asset_copy_record modify update_by varchar(24);
alter table eam_asset_copy_record modify delete_by varchar(24);
alter table eam_asset_copy_record modify tenant_id varchar(24);
alter table eam_asset_data_change modify create_by varchar(24);
alter table eam_asset_data_change modify update_by varchar(24);
alter table eam_asset_data_change modify delete_by varchar(24);
alter table eam_asset_data_change modify tenant_id varchar(24);
alter table eam_asset_data_permissions modify create_by varchar(24);
alter table eam_asset_data_permissions modify update_by varchar(24);
alter table eam_asset_data_permissions modify delete_by varchar(24);
alter table eam_asset_data_permissions modify tenant_id varchar(24);
alter table eam_asset_data_permissions_catalog modify create_by varchar(24);
alter table eam_asset_data_permissions_catalog modify update_by varchar(24);
alter table eam_asset_data_permissions_catalog modify delete_by varchar(24);
alter table eam_asset_data_permissions_o_org modify create_by varchar(24);
alter table eam_asset_data_permissions_o_org modify update_by varchar(24);
alter table eam_asset_data_permissions_o_org modify delete_by varchar(24);
alter table eam_asset_data_permissions_org modify create_by varchar(24);
alter table eam_asset_data_permissions_org modify update_by varchar(24);
alter table eam_asset_data_permissions_org modify delete_by varchar(24);
alter table eam_asset_data_permissions_position modify create_by varchar(24);
alter table eam_asset_data_permissions_position modify update_by varchar(24);
alter table eam_asset_data_permissions_position modify delete_by varchar(24);
alter table eam_asset_data_permissions_wh modify create_by varchar(24);
alter table eam_asset_data_permissions_wh modify update_by varchar(24);
alter table eam_asset_data_permissions_wh modify delete_by varchar(24);
alter table eam_asset_data_range modify create_by varchar(24);
alter table eam_asset_data_range modify update_by varchar(24);
alter table eam_asset_data_range modify delete_by varchar(24);
alter table eam_asset_data_range modify tenant_id varchar(24);
alter table eam_asset_depreciation modify create_by varchar(24);
alter table eam_asset_depreciation modify update_by varchar(24);
alter table eam_asset_depreciation modify delete_by varchar(24);
alter table eam_asset_depreciation modify tenant_id varchar(24);
alter table eam_asset_depreciation_cal_rule modify create_by varchar(24);
alter table eam_asset_depreciation_cal_rule modify update_by varchar(24);
alter table eam_asset_depreciation_cal_rule modify delete_by varchar(24);
alter table eam_asset_depreciation_cal_rule modify tenant_id varchar(24);
alter table eam_asset_depreciation_category modify create_by varchar(24);
alter table eam_asset_depreciation_category modify update_by varchar(24);
alter table eam_asset_depreciation_category modify delete_by varchar(24);
alter table eam_asset_depreciation_detail modify create_by varchar(24);
alter table eam_asset_depreciation_detail modify update_by varchar(24);
alter table eam_asset_depreciation_detail modify delete_by varchar(24);
alter table eam_asset_depreciation_exclude modify create_by varchar(24);
alter table eam_asset_depreciation_exclude modify update_by varchar(24);
alter table eam_asset_depreciation_exclude modify delete_by varchar(24);
alter table eam_asset_depreciation_history modify create_by varchar(24);
alter table eam_asset_depreciation_history modify update_by varchar(24);
alter table eam_asset_depreciation_history modify delete_by varchar(24);
alter table eam_asset_depreciation_history modify tenant_id varchar(24);
alter table eam_asset_depreciation_oper modify create_by varchar(24);
alter table eam_asset_depreciation_oper modify update_by varchar(24);
alter table eam_asset_depreciation_oper modify delete_by varchar(24);
alter table eam_asset_depreciation_oper modify tenant_id varchar(24);
alter table eam_asset_depreciation_status modify create_by varchar(24);
alter table eam_asset_depreciation_status modify update_by varchar(24);
alter table eam_asset_depreciation_status modify delete_by varchar(24);
alter table eam_asset_depreciation_status modify tenant_id varchar(24);
alter table eam_asset_discovery modify create_by varchar(24);
alter table eam_asset_discovery modify update_by varchar(24);
alter table eam_asset_discovery modify delete_by varchar(24);
alter table eam_asset_employee_apply modify create_by varchar(24);
alter table eam_asset_employee_apply modify update_by varchar(24);
alter table eam_asset_employee_apply modify delete_by varchar(24);
alter table eam_asset_employee_apply modify tenant_id varchar(24);
alter table eam_asset_employee_handover modify create_by varchar(24);
alter table eam_asset_employee_handover modify update_by varchar(24);
alter table eam_asset_employee_handover modify delete_by varchar(24);
alter table eam_asset_employee_handover modify tenant_id varchar(24);
alter table eam_asset_employee_loss modify create_by varchar(24);
alter table eam_asset_employee_loss modify update_by varchar(24);
alter table eam_asset_employee_loss modify delete_by varchar(24);
alter table eam_asset_employee_loss modify tenant_id varchar(24);
alter table eam_asset_employee_repair modify create_by varchar(24);
alter table eam_asset_employee_repair modify update_by varchar(24);
alter table eam_asset_employee_repair modify delete_by varchar(24);
alter table eam_asset_employee_repair modify tenant_id varchar(24);
alter table eam_asset_handle modify create_by varchar(24);
alter table eam_asset_handle modify update_by varchar(24);
alter table eam_asset_handle modify delete_by varchar(24);
alter table eam_asset_handle modify tenant_id varchar(24);
alter table eam_asset_handle_type modify create_by varchar(24);
alter table eam_asset_handle_type modify update_by varchar(24);
alter table eam_asset_handle_type modify delete_by varchar(24);
alter table eam_asset_handle_type modify tenant_id varchar(24);
alter table eam_asset_item modify create_by varchar(24);
alter table eam_asset_item modify update_by varchar(24);
alter table eam_asset_item modify delete_by varchar(24);
alter table eam_asset_label modify create_by varchar(24);
alter table eam_asset_label modify update_by varchar(24);
alter table eam_asset_label modify delete_by varchar(24);
alter table eam_asset_label modify tenant_id varchar(24);
alter table eam_asset_label_col modify create_by varchar(24);
alter table eam_asset_label_col modify update_by varchar(24);
alter table eam_asset_label_col modify delete_by varchar(24);
alter table eam_asset_label_col modify tenant_id varchar(24);
alter table eam_asset_label_layout modify create_by varchar(24);
alter table eam_asset_label_layout modify update_by varchar(24);
alter table eam_asset_label_layout modify delete_by varchar(24);
alter table eam_asset_label_layout_bak modify create_by varchar(24);
alter table eam_asset_label_layout_bak modify update_by varchar(24);
alter table eam_asset_label_layout_bak modify delete_by varchar(24);
alter table eam_asset_label_layout_def modify create_by varchar(24);
alter table eam_asset_label_layout_def modify update_by varchar(24);
alter table eam_asset_label_layout_def modify delete_by varchar(24);
alter table eam_asset_label_layout_s1 modify create_by varchar(24);
alter table eam_asset_label_layout_s1 modify update_by varchar(24);
alter table eam_asset_label_layout_s1 modify delete_by varchar(24);
alter table eam_asset_label_layout_s2 modify create_by varchar(24);
alter table eam_asset_label_layout_s2 modify update_by varchar(24);
alter table eam_asset_label_layout_s2 modify delete_by varchar(24);
alter table eam_asset_label_paper modify create_by varchar(24);
alter table eam_asset_label_paper modify update_by varchar(24);
alter table eam_asset_label_paper modify delete_by varchar(24);
alter table eam_asset_label_paper modify tenant_id varchar(24);
alter table eam_asset_label_tpl modify create_by varchar(24);
alter table eam_asset_label_tpl modify update_by varchar(24);
alter table eam_asset_label_tpl modify delete_by varchar(24);
alter table eam_asset_label_tpl modify tenant_id varchar(24);
alter table eam_asset_label_tpl_item modify create_by varchar(24);
alter table eam_asset_label_tpl_item modify update_by varchar(24);
alter table eam_asset_label_tpl_item modify delete_by varchar(24);
alter table eam_asset_maintenance modify create_by varchar(24);
alter table eam_asset_maintenance modify update_by varchar(24);
alter table eam_asset_maintenance modify delete_by varchar(24);
alter table eam_asset_maintenance modify tenant_id varchar(24);
alter table eam_asset_maintenance_record modify create_by varchar(24);
alter table eam_asset_maintenance_record modify update_by varchar(24);
alter table eam_asset_maintenance_record modify delete_by varchar(24);
alter table eam_asset_maintenance_record modify tenant_id varchar(24);
alter table eam_asset_maintenance_record_u modify create_by varchar(24);
alter table eam_asset_maintenance_record_u modify update_by varchar(24);
alter table eam_asset_maintenance_record_u modify delete_by varchar(24);
alter table eam_asset_maintenance_record_u modify tenant_id varchar(24);
alter table eam_asset_maintenance_update modify create_by varchar(24);
alter table eam_asset_maintenance_update modify update_by varchar(24);
alter table eam_asset_maintenance_update modify delete_by varchar(24);
alter table eam_asset_maintenance_update modify tenant_id varchar(24);
alter table eam_asset_my_apply modify create_by varchar(24);
alter table eam_asset_my_apply modify update_by varchar(24);
alter table eam_asset_my_apply modify delete_by varchar(24);
alter table eam_asset_my_apply modify tenant_id varchar(24);
alter table eam_asset_my_apply_list modify create_by varchar(24);
alter table eam_asset_my_apply_list modify update_by varchar(24);
alter table eam_asset_my_apply_list modify delete_by varchar(24);
alter table eam_asset_my_apply_list modify tenant_id varchar(24);
alter table eam_asset_oper_lock modify create_by varchar(24);
alter table eam_asset_oper_lock modify update_by varchar(24);
alter table eam_asset_oper_lock modify delete_by varchar(24);
alter table eam_asset_pos_change modify create_by varchar(24);
alter table eam_asset_pos_change modify update_by varchar(24);
alter table eam_asset_pos_change modify delete_by varchar(24);
alter table eam_asset_process_record modify create_by varchar(24);
alter table eam_asset_process_record modify update_by varchar(24);
alter table eam_asset_process_record modify delete_by varchar(24);
alter table eam_asset_rack modify create_by varchar(24);
alter table eam_asset_rack modify update_by varchar(24);
alter table eam_asset_rack modify delete_by varchar(24);
alter table eam_asset_rack modify tenant_id varchar(24);
alter table eam_asset_rack_info modify create_by varchar(24);
alter table eam_asset_rack_info modify update_by varchar(24);
alter table eam_asset_rack_info modify delete_by varchar(24);
alter table eam_asset_rack_info modify tenant_id varchar(24);
alter table eam_asset_region modify create_by varchar(24);
alter table eam_asset_region modify update_by varchar(24);
alter table eam_asset_region modify delete_by varchar(24);
alter table eam_asset_region modify tenant_id varchar(24);
alter table eam_asset_repair modify create_by varchar(24);
alter table eam_asset_repair modify update_by varchar(24);
alter table eam_asset_repair modify delete_by varchar(24);
alter table eam_asset_repair modify tenant_id varchar(24);
alter table eam_asset_running_situation modify update_by varchar(24);
alter table eam_asset_running_situation modify delete_by varchar(24);
alter table eam_asset_scan_scene modify id varchar(24);
alter table eam_asset_scan_scene modify create_by varchar(24);
alter table eam_asset_scan_scene modify update_by varchar(24);
alter table eam_asset_scan_scene modify delete_by varchar(24);
alter table eam_asset_scan_scene modify tenant_id varchar(24);
alter table eam_asset_scrap modify create_by varchar(24);
alter table eam_asset_scrap modify update_by varchar(24);
alter table eam_asset_scrap modify delete_by varchar(24);
alter table eam_asset_scrap modify tenant_id varchar(24);
alter table eam_asset_selected_data modify create_by varchar(24);
alter table eam_asset_selected_data modify update_by varchar(24);
alter table eam_asset_selected_data modify delete_by varchar(24);
alter table eam_asset_software modify create_by varchar(24);
alter table eam_asset_software modify update_by varchar(24);
alter table eam_asset_software modify delete_by varchar(24);
alter table eam_asset_software modify tenant_id varchar(24);
alter table eam_asset_software_change modify create_by varchar(24);
alter table eam_asset_software_change modify update_by varchar(24);
alter table eam_asset_software_change modify delete_by varchar(24);
alter table eam_asset_software_change modify tenant_id varchar(24);
alter table eam_asset_software_change_detail modify create_by varchar(24);
alter table eam_asset_software_change_detail modify update_by varchar(24);
alter table eam_asset_software_change_detail modify delete_by varchar(24);
alter table eam_asset_software_distribute modify create_by varchar(24);
alter table eam_asset_software_distribute modify update_by varchar(24);
alter table eam_asset_software_distribute modify delete_by varchar(24);
alter table eam_asset_software_distribute modify tenant_id varchar(24);
alter table eam_asset_software_distribute_data modify create_by varchar(24);
alter table eam_asset_software_distribute_data modify update_by varchar(24);
alter table eam_asset_software_distribute_data modify delete_by varchar(24);
alter table eam_asset_software_maintenance modify create_by varchar(24);
alter table eam_asset_software_maintenance modify update_by varchar(24);
alter table eam_asset_software_maintenance modify delete_by varchar(24);
alter table eam_asset_software_maintenance modify tenant_id varchar(24);
alter table eam_asset_software_maintenance_detail modify create_by varchar(24);
alter table eam_asset_software_maintenance_detail modify update_by varchar(24);
alter table eam_asset_software_maintenance_detail modify delete_by varchar(24);
alter table eam_asset_status modify create_by varchar(24);
alter table eam_asset_status modify update_by varchar(24);
alter table eam_asset_status modify delete_by varchar(24);
alter table eam_asset_status modify tenant_id varchar(24);
alter table eam_asset_status_rule modify create_by varchar(24);
alter table eam_asset_status_rule modify update_by varchar(24);
alter table eam_asset_status_rule modify delete_by varchar(24);
alter table eam_asset_status_rule modify tenant_id varchar(24);
alter table eam_asset_status_rule_v modify create_by varchar(24);
alter table eam_asset_status_rule_v modify update_by varchar(24);
alter table eam_asset_status_rule_v modify delete_by varchar(24);
alter table eam_asset_status_rule_v modify tenant_id varchar(24);
alter table eam_asset_stock_collection modify create_by varchar(24);
alter table eam_asset_stock_collection modify update_by varchar(24);
alter table eam_asset_stock_collection modify delete_by varchar(24);
alter table eam_asset_stock_collection modify tenant_id varchar(24);
alter table eam_asset_stock_deliver modify create_by varchar(24);
alter table eam_asset_stock_deliver modify update_by varchar(24);
alter table eam_asset_stock_deliver modify delete_by varchar(24);
alter table eam_asset_stock_deliver modify tenant_id varchar(24);
alter table eam_asset_stock_goods modify create_by varchar(24);
alter table eam_asset_stock_goods modify update_by varchar(24);
alter table eam_asset_stock_goods modify delete_by varchar(24);
alter table eam_asset_stock_goods modify tenant_id varchar(24);
alter table eam_asset_stock_goods_adjust modify create_by varchar(24);
alter table eam_asset_stock_goods_adjust modify update_by varchar(24);
alter table eam_asset_stock_goods_adjust modify delete_by varchar(24);
alter table eam_asset_stock_goods_adjust modify tenant_id varchar(24);
alter table eam_asset_stock_goods_detail modify create_by varchar(24);
alter table eam_asset_stock_goods_detail modify update_by varchar(24);
alter table eam_asset_stock_goods_detail modify delete_by varchar(24);
alter table eam_asset_stock_goods_in modify create_by varchar(24);
alter table eam_asset_stock_goods_in modify update_by varchar(24);
alter table eam_asset_stock_goods_in modify delete_by varchar(24);
alter table eam_asset_stock_goods_in modify tenant_id varchar(24);
alter table eam_asset_stock_goods_out modify create_by varchar(24);
alter table eam_asset_stock_goods_out modify update_by varchar(24);
alter table eam_asset_stock_goods_out modify delete_by varchar(24);
alter table eam_asset_stock_goods_out modify tenant_id varchar(24);
alter table eam_asset_stock_goods_tranfer modify create_by varchar(24);
alter table eam_asset_stock_goods_tranfer modify update_by varchar(24);
alter table eam_asset_stock_goods_tranfer modify delete_by varchar(24);
alter table eam_asset_stock_goods_tranfer modify tenant_id varchar(24);
alter table eam_asset_stock_goods_use modify create_by varchar(24);
alter table eam_asset_stock_goods_use modify update_by varchar(24);
alter table eam_asset_stock_goods_use modify delete_by varchar(24);
alter table eam_asset_stock_goods_use modify tenant_id varchar(24);
alter table eam_asset_storage modify create_by varchar(24);
alter table eam_asset_storage modify update_by varchar(24);
alter table eam_asset_storage modify delete_by varchar(24);
alter table eam_asset_storage modify tenant_id varchar(24);
alter table eam_asset_trace modify create_by varchar(24);
alter table eam_asset_trace modify update_by varchar(24);
alter table eam_asset_trace modify delete_by varchar(24);
alter table eam_asset_trace modify tenant_id varchar(24);
alter table eam_asset_tranfer modify create_by varchar(24);
alter table eam_asset_tranfer modify update_by varchar(24);
alter table eam_asset_tranfer modify delete_by varchar(24);
alter table eam_asset_tranfer modify tenant_id varchar(24);
alter table eam_bill_relation modify create_by varchar(24);
alter table eam_bill_relation modify update_by varchar(24);
alter table eam_bill_relation modify delete_by varchar(24);
alter table eam_brand modify create_by varchar(24);
alter table eam_brand modify update_by varchar(24);
alter table eam_brand modify delete_by varchar(24);
alter table eam_brand modify tenant_id varchar(24);
alter table eam_brand_demo modify create_by varchar(24);
alter table eam_brand_demo modify update_by varchar(24);
alter table eam_brand_demo modify delete_by varchar(24);
alter table eam_brand_demo modify tenant_id varchar(24);
alter table eam_c1_brand modify create_by varchar(24);
alter table eam_c1_brand modify update_by varchar(24);
alter table eam_c1_brand modify delete_by varchar(24);
alter table eam_c1_brand modify tenant_id varchar(24);
alter table eam_c1_mapping modify create_by varchar(24);
alter table eam_c1_mapping modify update_by varchar(24);
alter table eam_c1_mapping modify delete_by varchar(24);
alter table eam_c1_qh_fa_additions modify create_by varchar(24);
alter table eam_c1_qh_fa_additions modify update_by varchar(24);
alter table eam_c1_qh_fa_additions modify delete_by varchar(24);
alter table eam_c1_sync_asset modify create_by varchar(24);
alter table eam_c1_sync_asset modify update_by varchar(24);
alter table eam_c1_sync_asset modify delete_by varchar(24);
alter table eam_c1_sync_asset_record modify create_by varchar(24);
alter table eam_c1_sync_asset_record modify update_by varchar(24);
alter table eam_c1_sync_asset_record modify delete_by varchar(24);
alter table eam_c_cust_inspect_item modify create_by varchar(24);
alter table eam_c_cust_inspect_item modify update_by varchar(24);
alter table eam_c_cust_inspect_item modify delete_by varchar(24);
alter table eam_c_cust_inspect_log modify create_by varchar(24);
alter table eam_c_cust_inspect_log modify update_by varchar(24);
alter table eam_c_cust_inspect_log modify delete_by varchar(24);
alter table eam_c_cust_inspect_plan modify create_by varchar(24);
alter table eam_c_cust_inspect_plan modify update_by varchar(24);
alter table eam_c_cust_inspect_plan modify delete_by varchar(24);
alter table eam_c_cust_inspect_plan modify tenant_id varchar(24);
alter table eam_c_cust_inspect_task modify create_by varchar(24);
alter table eam_c_cust_inspect_task modify update_by varchar(24);
alter table eam_c_cust_inspect_task modify delete_by varchar(24);
alter table eam_c_cust_inspect_task modify tenant_id varchar(24);
alter table eam_c_cust_inspect_tpl modify create_by varchar(24);
alter table eam_c_cust_inspect_tpl modify update_by varchar(24);
alter table eam_c_cust_inspect_tpl modify delete_by varchar(24);
alter table eam_c_cust_inspect_tpl modify tenant_id varchar(24);
alter table eam_c_cust_inspect_tpl_asset modify create_by varchar(24);
alter table eam_c_cust_inspect_tpl_asset modify update_by varchar(24);
alter table eam_c_cust_inspect_tpl_asset modify delete_by varchar(24);
alter table eam_c_cust_inspect_user_r modify create_by varchar(24);
alter table eam_c_cust_inspect_user_r modify update_by varchar(24);
alter table eam_c_cust_inspect_user_r modify delete_by varchar(24);
alter table eam_c_cust_inspect_user_r modify tenant_id varchar(24);
alter table eam_c_cust_inspect_user_s modify create_by varchar(24);
alter table eam_c_cust_inspect_user_s modify update_by varchar(24);
alter table eam_c_cust_inspect_user_s modify delete_by varchar(24);
alter table eam_c_cust_repair_apply modify create_by varchar(24);
alter table eam_c_cust_repair_apply modify update_by varchar(24);
alter table eam_c_cust_repair_apply modify delete_by varchar(24);
alter table eam_c_cust_repair_type modify create_by varchar(24);
alter table eam_c_cust_repair_type modify update_by varchar(24);
alter table eam_c_cust_repair_type modify delete_by varchar(24);
alter table eam_c_cust_repiar_item modify create_by varchar(24);
alter table eam_c_cust_repiar_item modify update_by varchar(24);
alter table eam_c_cust_repiar_item modify delete_by varchar(24);
alter table eam_category modify create_by varchar(24);
alter table eam_category modify update_by varchar(24);
alter table eam_category modify delete_by varchar(24);
alter table eam_category_ext modify create_by varchar(24);
alter table eam_category_ext modify update_by varchar(24);
alter table eam_category_ext modify delete_by varchar(24);
alter table eam_category_finance modify create_by varchar(24);
alter table eam_category_finance modify update_by varchar(24);
alter table eam_category_finance modify delete_by varchar(24);
alter table eam_category_finance modify tenant_id varchar(24);
alter table eam_category_priv modify create_by varchar(24);
alter table eam_category_priv modify update_by varchar(24);
alter table eam_category_priv modify delete_by varchar(24);
alter table eam_check_group modify create_by varchar(24);
alter table eam_check_group modify update_by varchar(24);
alter table eam_check_group modify delete_by varchar(24);
alter table eam_check_group modify tenant_id varchar(24);
alter table eam_check_group_item modify create_by varchar(24);
alter table eam_check_group_item modify update_by varchar(24);
alter table eam_check_group_item modify delete_by varchar(24);
alter table eam_check_group_item modify tenant_id varchar(24);
alter table eam_check_item modify create_by varchar(24);
alter table eam_check_item modify update_by varchar(24);
alter table eam_check_item modify delete_by varchar(24);
alter table eam_check_item modify tenant_id varchar(24);
alter table eam_check_select modify create_by varchar(24);
alter table eam_check_select modify update_by varchar(24);
alter table eam_check_select modify delete_by varchar(24);
alter table eam_code_part modify create_by varchar(24);
alter table eam_code_part modify update_by varchar(24);
alter table eam_code_part modify delete_by varchar(24);
alter table eam_code_part modify tenant_id varchar(24);
alter table eam_code_rule modify create_by varchar(24);
alter table eam_code_rule modify update_by varchar(24);
alter table eam_code_rule modify delete_by varchar(24);
alter table eam_code_rule modify tenant_id varchar(24);
alter table eam_demo_c1 modify id varchar(24);
alter table eam_demo_c1 modify create_by varchar(24);
alter table eam_demo_c1 modify update_by varchar(24);
alter table eam_demo_c1 modify delete_by varchar(24);
alter table eam_demo_c2 modify id varchar(24);
alter table eam_demo_c2 modify create_by varchar(24);
alter table eam_demo_c2 modify update_by varchar(24);
alter table eam_demo_c2 modify delete_by varchar(24);
alter table eam_device_associate modify create_by varchar(24);
alter table eam_device_associate modify update_by varchar(24);
alter table eam_device_associate modify delete_by varchar(24);
alter table eam_device_associate modify tenant_id varchar(24);
alter table eam_device_associate_item modify create_by varchar(24);
alter table eam_device_associate_item modify update_by varchar(24);
alter table eam_device_associate_item modify delete_by varchar(24);
alter table eam_device_sp modify create_by varchar(24);
alter table eam_device_sp modify update_by varchar(24);
alter table eam_device_sp modify delete_by varchar(24);
alter table eam_device_sp modify tenant_id varchar(24);
alter table eam_device_sp_associate modify create_by varchar(24);
alter table eam_device_sp_associate modify update_by varchar(24);
alter table eam_device_sp_associate modify delete_by varchar(24);
alter table eam_device_sp_rcd modify create_by varchar(24);
alter table eam_device_sp_rcd modify update_by varchar(24);
alter table eam_device_sp_rcd modify delete_by varchar(24);
alter table eam_device_sp_status modify create_by varchar(24);
alter table eam_device_sp_status modify update_by varchar(24);
alter table eam_device_sp_status modify delete_by varchar(24);
alter table eam_device_sp_type modify create_by varchar(24);
alter table eam_device_sp_type modify update_by varchar(24);
alter table eam_device_sp_type modify delete_by varchar(24);
alter table eam_device_sp_type modify tenant_id varchar(24);
alter table eam_failure_registration modify create_by varchar(24);
alter table eam_failure_registration modify update_by varchar(24);
alter table eam_failure_registration modify delete_by varchar(24);
alter table eam_failure_registration modify tenant_id varchar(24);
alter table eam_feedback modify content varchar(24);
alter table eam_feedback modify attach varchar(24);
alter table eam_feedback modify create_by varchar(24);
alter table eam_feedback modify update_by varchar(24);
alter table eam_feedback modify delete_by varchar(24);
alter table eam_feedback modify tenant_id varchar(24);
alter table eam_goods modify create_by varchar(24);
alter table eam_goods modify update_by varchar(24);
alter table eam_goods modify delete_by varchar(24);
alter table eam_goods modify tenant_id varchar(24);
alter table eam_goods_stock modify create_by varchar(24);
alter table eam_goods_stock modify update_by varchar(24);
alter table eam_goods_stock modify delete_by varchar(24);
alter table eam_goods_stock modify tenant_id varchar(24);
alter table eam_goods_stock_related modify create_by varchar(24);
alter table eam_goods_stock_related modify update_by varchar(24);
alter table eam_goods_stock_related modify delete_by varchar(24);
alter table eam_goods_stock_usage modify create_by varchar(24);
alter table eam_goods_stock_usage modify update_by varchar(24);
alter table eam_goods_stock_usage modify delete_by varchar(24);
alter table eam_group_user modify create_by varchar(24);
alter table eam_group_user modify update_by varchar(24);
alter table eam_group_user modify delete_by varchar(24);
alter table eam_inspect_point_item modify create_by varchar(24);
alter table eam_inspect_point_item modify update_by varchar(24);
alter table eam_inspect_point_item modify delete_by varchar(24);
alter table eam_inspection_group modify create_by varchar(24);
alter table eam_inspection_group modify update_by varchar(24);
alter table eam_inspection_group modify delete_by varchar(24);
alter table eam_inspection_group modify tenant_id varchar(24);
alter table eam_inspection_group_user modify create_by varchar(24);
alter table eam_inspection_group_user modify update_by varchar(24);
alter table eam_inspection_group_user modify delete_by varchar(24);
alter table eam_inspection_log modify create_by varchar(24);
alter table eam_inspection_log modify update_by varchar(24);
alter table eam_inspection_log modify delete_by varchar(24);
alter table eam_inspection_plan modify create_by varchar(24);
alter table eam_inspection_plan modify update_by varchar(24);
alter table eam_inspection_plan modify delete_by varchar(24);
alter table eam_inspection_plan modify tenant_id varchar(24);
alter table eam_inspection_plan_point modify create_by varchar(24);
alter table eam_inspection_plan_point modify update_by varchar(24);
alter table eam_inspection_plan_point modify delete_by varchar(24);
alter table eam_inspection_plan_point modify tenant_id varchar(24);
alter table eam_inspection_point modify content varchar(24);
alter table eam_inspection_point modify create_by varchar(24);
alter table eam_inspection_point modify update_by varchar(24);
alter table eam_inspection_point modify delete_by varchar(24);
alter table eam_inspection_point modify tenant_id varchar(24);
alter table eam_inspection_point_item modify create_by varchar(24);
alter table eam_inspection_point_item modify update_by varchar(24);
alter table eam_inspection_point_item modify delete_by varchar(24);
alter table eam_inspection_point_owner modify create_by varchar(24);
alter table eam_inspection_point_owner modify update_by varchar(24);
alter table eam_inspection_point_owner modify delete_by varchar(24);
alter table eam_inspection_point_owner modify tenant_id varchar(24);
alter table eam_inspection_point_pos modify create_by varchar(24);
alter table eam_inspection_point_pos modify update_by varchar(24);
alter table eam_inspection_point_pos modify delete_by varchar(24);
alter table eam_inspection_point_pos modify tenant_id varchar(24);
alter table eam_inspection_process_action modify create_by varchar(24);
alter table eam_inspection_process_action modify update_by varchar(24);
alter table eam_inspection_process_action modify delete_by varchar(24);
alter table eam_inspection_process_action modify tenant_id varchar(24);
alter table eam_inspection_rcd modify create_by varchar(24);
alter table eam_inspection_rcd modify update_by varchar(24);
alter table eam_inspection_rcd modify delete_by varchar(24);
alter table eam_inspection_route modify create_by varchar(24);
alter table eam_inspection_route modify update_by varchar(24);
alter table eam_inspection_route modify delete_by varchar(24);
alter table eam_inspection_route modify tenant_id varchar(24);
alter table eam_inspection_task modify create_by varchar(24);
alter table eam_inspection_task modify update_by varchar(24);
alter table eam_inspection_task modify delete_by varchar(24);
alter table eam_inspection_task modify tenant_id varchar(24);
alter table eam_inspection_task_abnormal modify create_by varchar(24);
alter table eam_inspection_task_abnormal modify update_by varchar(24);
alter table eam_inspection_task_abnormal modify delete_by varchar(24);
alter table eam_inspection_task_abnormal modify tenant_id varchar(24);
alter table eam_inspection_task_point modify point_content varchar(24);
alter table eam_inspection_task_point modify create_by varchar(24);
alter table eam_inspection_task_point modify update_by varchar(24);
alter table eam_inspection_task_point modify delete_by varchar(24);
alter table eam_inspection_task_point_oper modify create_by varchar(24);
alter table eam_inspection_task_point_oper modify update_by varchar(24);
alter table eam_inspection_task_point_oper modify delete_by varchar(24);
alter table eam_inventory modify create_by varchar(24);
alter table eam_inventory modify update_by varchar(24);
alter table eam_inventory modify delete_by varchar(24);
alter table eam_inventory modify tenant_id varchar(24);
alter table eam_inventory_asset modify create_by varchar(24);
alter table eam_inventory_asset modify update_by varchar(24);
alter table eam_inventory_asset modify delete_by varchar(24);
alter table eam_inventory_catalog modify create_by varchar(24);
alter table eam_inventory_catalog modify update_by varchar(24);
alter table eam_inventory_catalog modify delete_by varchar(24);
alter table eam_inventory_director modify create_by varchar(24);
alter table eam_inventory_director modify update_by varchar(24);
alter table eam_inventory_director modify delete_by varchar(24);
alter table eam_inventory_employee modify create_by varchar(24);
alter table eam_inventory_employee modify update_by varchar(24);
alter table eam_inventory_employee modify delete_by varchar(24);
alter table eam_inventory_employee modify tenant_id varchar(24);
alter table eam_inventory_feedback modify create_by varchar(24);
alter table eam_inventory_feedback modify update_by varchar(24);
alter table eam_inventory_feedback modify delete_by varchar(24);
alter table eam_inventory_feedback_msg modify create_by varchar(24);
alter table eam_inventory_feedback_msg modify update_by varchar(24);
alter table eam_inventory_feedback_msg modify delete_by varchar(24);
alter table eam_inventory_manager modify create_by varchar(24);
alter table eam_inventory_manager modify update_by varchar(24);
alter table eam_inventory_manager modify delete_by varchar(24);
alter table eam_inventory_plan modify create_by varchar(24);
alter table eam_inventory_plan modify update_by varchar(24);
alter table eam_inventory_plan modify delete_by varchar(24);
alter table eam_inventory_plan modify tenant_id varchar(24);
alter table eam_inventory_position modify create_by varchar(24);
alter table eam_inventory_position modify update_by varchar(24);
alter table eam_inventory_position modify delete_by varchar(24);
alter table eam_inventory_user modify create_by varchar(24);
alter table eam_inventory_user modify update_by varchar(24);
alter table eam_inventory_user modify delete_by varchar(24);
alter table eam_inventory_warehouse modify create_by varchar(24);
alter table eam_inventory_warehouse modify update_by varchar(24);
alter table eam_inventory_warehouse modify delete_by varchar(24);
alter table eam_maintain_group modify create_by varchar(24);
alter table eam_maintain_group modify update_by varchar(24);
alter table eam_maintain_group modify delete_by varchar(24);
alter table eam_maintain_group modify tenant_id varchar(24);
alter table eam_maintain_plan modify create_by varchar(24);
alter table eam_maintain_plan modify update_by varchar(24);
alter table eam_maintain_plan modify delete_by varchar(24);
alter table eam_maintain_plan modify tenant_id varchar(24);
alter table eam_maintain_project modify create_by varchar(24);
alter table eam_maintain_project modify update_by varchar(24);
alter table eam_maintain_project modify delete_by varchar(24);
alter table eam_maintain_project modify tenant_id varchar(24);
alter table eam_maintain_project_select modify create_by varchar(24);
alter table eam_maintain_project_select modify update_by varchar(24);
alter table eam_maintain_project_select modify delete_by varchar(24);
alter table eam_maintain_rcd modify create_by varchar(24);
alter table eam_maintain_rcd modify update_by varchar(24);
alter table eam_maintain_rcd modify delete_by varchar(24);
alter table eam_maintain_task modify create_by varchar(24);
alter table eam_maintain_task modify update_by varchar(24);
alter table eam_maintain_task modify delete_by varchar(24);
alter table eam_maintain_task modify tenant_id varchar(24);
alter table eam_maintain_task_project modify create_by varchar(24);
alter table eam_maintain_task_project modify update_by varchar(24);
alter table eam_maintain_task_project modify delete_by varchar(24);
alter table eam_maintainer modify create_by varchar(24);
alter table eam_maintainer modify update_by varchar(24);
alter table eam_maintainer modify delete_by varchar(24);
alter table eam_maintainer modify tenant_id varchar(24);
alter table eam_manufacturer modify create_by varchar(24);
alter table eam_manufacturer modify update_by varchar(24);
alter table eam_manufacturer modify delete_by varchar(24);
alter table eam_manufacturer modify tenant_id varchar(24);
alter table eam_mapping_owner modify create_by varchar(24);
alter table eam_mapping_owner modify update_by varchar(24);
alter table eam_mapping_owner modify delete_by varchar(24);
alter table eam_operate modify create_by varchar(24);
alter table eam_operate modify update_by varchar(24);
alter table eam_operate modify delete_by varchar(24);
alter table eam_operate modify tenant_id varchar(24);
alter table eam_plan_execute_log modify create_by varchar(24);
alter table eam_plan_execute_log modify update_by varchar(24);
alter table eam_plan_execute_log modify delete_by varchar(24);
alter table eam_position modify create_by varchar(24);
alter table eam_position modify update_by varchar(24);
alter table eam_position modify delete_by varchar(24);
alter table eam_position modify tenant_id varchar(24);
alter table eam_position_rfid_rcd modify create_by varchar(24);
alter table eam_position_rfid_rcd modify update_by varchar(24);
alter table eam_position_rfid_rcd modify delete_by varchar(24);
alter table eam_purchase_apply modify create_by varchar(24);
alter table eam_purchase_apply modify update_by varchar(24);
alter table eam_purchase_apply modify delete_by varchar(24);
alter table eam_purchase_apply modify tenant_id varchar(24);
alter table eam_purchase_apply_item modify create_by varchar(24);
alter table eam_purchase_apply_item modify update_by varchar(24);
alter table eam_purchase_apply_item modify delete_by varchar(24);
alter table eam_purchase_apply_item modify tenant_id varchar(24);
alter table eam_purchase_check modify create_by varchar(24);
alter table eam_purchase_check modify update_by varchar(24);
alter table eam_purchase_check modify delete_by varchar(24);
alter table eam_purchase_check modify tenant_id varchar(24);
alter table eam_purchase_import modify create_by varchar(24);
alter table eam_purchase_import modify update_by varchar(24);
alter table eam_purchase_import modify delete_by varchar(24);
alter table eam_purchase_import modify tenant_id varchar(24);
alter table eam_purchase_order modify create_by varchar(24);
alter table eam_purchase_order modify update_by varchar(24);
alter table eam_purchase_order modify delete_by varchar(24);
alter table eam_purchase_order modify tenant_id varchar(24);
alter table eam_purchase_order_detail modify create_by varchar(24);
alter table eam_purchase_order_detail modify update_by varchar(24);
alter table eam_purchase_order_detail modify delete_by varchar(24);
alter table eam_purchase_order_detail modify tenant_id varchar(24);
alter table eam_related_items modify create_by varchar(24);
alter table eam_related_items modify update_by varchar(24);
alter table eam_related_items modify delete_by varchar(24);
alter table eam_repair_category modify create_by varchar(24);
alter table eam_repair_category modify update_by varchar(24);
alter table eam_repair_category modify delete_by varchar(24);
alter table eam_repair_category modify tenant_id varchar(24);
alter table eam_repair_category_tpl modify create_by varchar(24);
alter table eam_repair_category_tpl modify update_by varchar(24);
alter table eam_repair_category_tpl modify delete_by varchar(24);
alter table eam_repair_category_tpl modify tenant_id varchar(24);
alter table eam_repair_group modify create_by varchar(24);
alter table eam_repair_group modify update_by varchar(24);
alter table eam_repair_group modify delete_by varchar(24);
alter table eam_repair_group modify tenant_id varchar(24);
alter table eam_repair_order modify create_by varchar(24);
alter table eam_repair_order modify update_by varchar(24);
alter table eam_repair_order modify delete_by varchar(24);
alter table eam_repair_order modify tenant_id varchar(24);
alter table eam_repair_order_acceptance modify create_by varchar(24);
alter table eam_repair_order_acceptance modify update_by varchar(24);
alter table eam_repair_order_acceptance modify delete_by varchar(24);
alter table eam_repair_order_acceptance modify tenant_id varchar(24);
alter table eam_repair_order_act modify create_by varchar(24);
alter table eam_repair_order_act modify update_by varchar(24);
alter table eam_repair_order_act modify delete_by varchar(24);
alter table eam_repair_order_act modify tenant_id varchar(24);
alter table eam_repair_order_act_sp modify create_by varchar(24);
alter table eam_repair_order_act_sp modify update_by varchar(24);
alter table eam_repair_order_act_sp modify delete_by varchar(24);
alter table eam_repair_order_process modify create_by varchar(24);
alter table eam_repair_order_process modify update_by varchar(24);
alter table eam_repair_order_process modify delete_by varchar(24);
alter table eam_repair_order_transfer modify create_by varchar(24);
alter table eam_repair_order_transfer modify update_by varchar(24);
alter table eam_repair_order_transfer modify delete_by varchar(24);
alter table eam_repair_order_transfer modify tenant_id varchar(24);
alter table eam_repair_rcd modify create_by varchar(24);
alter table eam_repair_rcd modify update_by varchar(24);
alter table eam_repair_rcd modify delete_by varchar(24);
alter table eam_repair_rule modify create_by varchar(24);
alter table eam_repair_rule modify update_by varchar(24);
alter table eam_repair_rule modify delete_by varchar(24);
alter table eam_repair_rule modify tenant_id varchar(24);
alter table eam_repair_rule_item modify create_by varchar(24);
alter table eam_repair_rule_item modify update_by varchar(24);
alter table eam_repair_rule_item modify delete_by varchar(24);
alter table eam_repair_urgency modify create_by varchar(24);
alter table eam_repair_urgency modify update_by varchar(24);
alter table eam_repair_urgency modify delete_by varchar(24);
alter table eam_repair_urgency modify tenant_id varchar(24);
alter table eam_residual_strategy modify create_by varchar(24);
alter table eam_residual_strategy modify update_by varchar(24);
alter table eam_residual_strategy modify delete_by varchar(24);
alter table eam_residual_strategy modify tenant_id varchar(24);
alter table eam_rfid_label modify create_by varchar(24);
alter table eam_rfid_label modify update_by varchar(24);
alter table eam_rfid_label modify delete_by varchar(24);
alter table eam_rfid_rcd modify create_by varchar(24);
alter table eam_rfid_rcd modify update_by varchar(24);
alter table eam_rfid_rcd modify delete_by varchar(24);
alter table eam_rfid_release modify create_by varchar(24);
alter table eam_rfid_release modify update_by varchar(24);
alter table eam_rfid_release modify delete_by varchar(24);
alter table eam_rfid_release modify tenant_id varchar(24);
alter table eam_role_approve modify create_by varchar(24);
alter table eam_role_approve modify update_by varchar(24);
alter table eam_role_approve modify delete_by varchar(24);
alter table eam_role_approve modify tenant_id varchar(24);
alter table eam_safetylevel modify create_by varchar(24);
alter table eam_safetylevel modify update_by varchar(24);
alter table eam_safetylevel modify delete_by varchar(24);
alter table eam_safetylevel modify tenant_id varchar(24);
alter table eam_stock modify create_by varchar(24);
alter table eam_stock modify update_by varchar(24);
alter table eam_stock modify delete_by varchar(24);
alter table eam_stock modify tenant_id varchar(24);
alter table eam_stock_import modify create_by varchar(24);
alter table eam_stock_import modify update_by varchar(24);
alter table eam_stock_import modify delete_by varchar(24);
alter table eam_stock_import modify tenant_id varchar(24);
alter table eam_stock_inventory_asset modify create_by varchar(24);
alter table eam_stock_inventory_asset modify update_by varchar(24);
alter table eam_stock_inventory_asset modify delete_by varchar(24);
alter table eam_stock_inventory_plan modify create_by varchar(24);
alter table eam_stock_inventory_plan modify update_by varchar(24);
alter table eam_stock_inventory_plan modify delete_by varchar(24);
alter table eam_stock_inventory_plan modify tenant_id varchar(24);
alter table eam_stock_inventory_task modify create_by varchar(24);
alter table eam_stock_inventory_task modify update_by varchar(24);
alter table eam_stock_inventory_task modify delete_by varchar(24);
alter table eam_stock_inventory_task modify tenant_id varchar(24);
alter table eam_supplier modify create_by varchar(24);
alter table eam_supplier modify update_by varchar(24);
alter table eam_supplier modify delete_by varchar(24);
alter table eam_supplier modify tenant_id varchar(24);
alter table eam_tpl_file modify create_by varchar(24);
alter table eam_tpl_file modify update_by varchar(24);
alter table eam_tpl_file modify delete_by varchar(24);
alter table eam_tpl_file modify tenant_id varchar(24);
alter table eam_transfer modify create_by varchar(24);
alter table eam_transfer modify update_by varchar(24);
alter table eam_transfer modify delete_by varchar(24);
alter table eam_transfer modify tenant_id varchar(24);
alter table eam_user_create_action_log modify create_by varchar(24);
alter table eam_user_create_action_log modify update_by varchar(24);
alter table eam_user_create_action_log modify delete_by varchar(24);
alter table eam_user_create_action_log modify tenant_id varchar(24);
alter table eam_warehouse modify create_by varchar(24);
alter table eam_warehouse modify update_by varchar(24);
alter table eam_warehouse modify delete_by varchar(24);
alter table eam_warehouse modify tenant_id varchar(24);
alter table eam_warehouse_position modify create_by varchar(24);
alter table eam_warehouse_position modify update_by varchar(24);
alter table eam_warehouse_position modify delete_by varchar(24);
alter table eam_work_order modify create_by varchar(24);
alter table eam_work_order modify update_by varchar(24);
alter table eam_work_order modify delete_by varchar(24);
alter table eam_work_order_msg modify create_by varchar(24);
alter table eam_work_order_msg modify update_by varchar(24);
alter table eam_work_order_msg modify delete_by varchar(24);
alter table example_address modify id varchar(24);
alter table example_address modify create_by varchar(24);
alter table example_address modify update_by varchar(24);
alter table example_address modify delete_by varchar(24);
alter table example_goods modify id varchar(24);
alter table example_goods modify create_by varchar(24);
alter table example_goods modify update_by varchar(24);
alter table example_goods modify delete_by varchar(24);
alter table example_news modify id varchar(24);
alter table example_news modify create_by varchar(24);
alter table example_news modify update_by varchar(24);
alter table example_news modify delete_by varchar(24);
alter table example_order modify id varchar(24);
alter table example_order modify address_id varchar(24);
alter table example_order modify create_by varchar(24);
alter table example_order modify update_by varchar(24);
alter table example_order modify delete_by varchar(24);
alter table example_order_item modify id varchar(24);
alter table example_order_item modify order_id varchar(24);
alter table example_order_item modify goods_id varchar(24);
alter table example_order_item modify create_by varchar(24);
alter table example_order_item modify update_by varchar(24);
alter table example_order_item modify delete_by varchar(24);
alter table hr_annual_leave_rule modify id varchar(24);
alter table hr_annual_leave_rule modify create_by varchar(24);
alter table hr_annual_leave_rule modify update_by varchar(24);
alter table hr_annual_leave_rule modify delete_by varchar(24);
alter table hr_annual_leave_rule modify tenant_id varchar(24);
alter table hr_assessment_bill modify id varchar(24);
alter table hr_assessment_bill modify create_by varchar(24);
alter table hr_assessment_bill modify update_by varchar(24);
alter table hr_assessment_bill modify delete_by varchar(24);
alter table hr_assessment_bill_task modify id varchar(24);
alter table hr_assessment_bill_task modify assessor_id varchar(24);
alter table hr_assessment_bill_task modify create_by varchar(24);
alter table hr_assessment_bill_task modify update_by varchar(24);
alter table hr_assessment_bill_task modify delete_by varchar(24);
alter table hr_assessment_bill_task modify tenant_id varchar(24);
alter table hr_assessment_bill_task_dtl modify id varchar(24);
alter table hr_assessment_bill_task_dtl modify create_by varchar(24);
alter table hr_assessment_bill_task_dtl modify update_by varchar(24);
alter table hr_assessment_bill_task_dtl modify delete_by varchar(24);
alter table hr_assessment_bill_task_dtl modify tenant_id varchar(24);
alter table hr_assessment_bill_task_paper modify id varchar(24);
alter table hr_assessment_bill_task_paper modify create_by varchar(24);
alter table hr_assessment_bill_task_paper modify update_by varchar(24);
alter table hr_assessment_bill_task_paper modify delete_by varchar(24);
alter table hr_assessment_bill_task_paper modify tenant_id varchar(24);
alter table hr_assessment_bill_user_map modify id varchar(24);
alter table hr_assessment_bill_user_map modify create_by varchar(24);
alter table hr_assessment_bill_user_map modify update_by varchar(24);
alter table hr_assessment_bill_user_map modify delete_by varchar(24);
alter table hr_assessment_cycle modify id varchar(24);
alter table hr_assessment_cycle modify create_by varchar(24);
alter table hr_assessment_cycle modify update_by varchar(24);
alter table hr_assessment_cycle modify delete_by varchar(24);
alter table hr_assessment_cycle modify tenant_id varchar(24);
alter table hr_assessment_grade modify id varchar(24);
alter table hr_assessment_grade modify create_by varchar(24);
alter table hr_assessment_grade modify update_by varchar(24);
alter table hr_assessment_grade modify delete_by varchar(24);
alter table hr_assessment_grade modify tenant_id varchar(24);
alter table hr_assessment_indicator modify id varchar(24);
alter table hr_assessment_indicator modify create_by varchar(24);
alter table hr_assessment_indicator modify update_by varchar(24);
alter table hr_assessment_indicator modify delete_by varchar(24);
alter table hr_assessment_indicator modify tenant_id varchar(24);
alter table hr_assessment_indicator_value modify id varchar(24);
alter table hr_assessment_indicator_value modify create_by varchar(24);
alter table hr_assessment_indicator_value modify update_by varchar(24);
alter table hr_assessment_indicator_value modify delete_by varchar(24);
alter table hr_assessment_indicator_value modify tenant_id varchar(24);
alter table hr_assessment_method modify id varchar(24);
alter table hr_assessment_method modify create_by varchar(24);
alter table hr_assessment_method modify update_by varchar(24);
alter table hr_assessment_method modify delete_by varchar(24);
alter table hr_assessment_method modify tenant_id varchar(24);
alter table hr_assessment_plan modify id varchar(24);
alter table hr_assessment_plan modify create_by varchar(24);
alter table hr_assessment_plan modify update_by varchar(24);
alter table hr_assessment_plan modify delete_by varchar(24);
alter table hr_assessment_plan modify tenant_id varchar(24);
alter table hr_assessment_task modify id varchar(24);
alter table hr_assessment_task modify create_by varchar(24);
alter table hr_assessment_task modify update_by varchar(24);
alter table hr_assessment_task modify delete_by varchar(24);
alter table hr_assessment_task modify tenant_id varchar(24);
alter table hr_assessment_task_dtl modify id varchar(24);
alter table hr_assessment_task_dtl modify create_by varchar(24);
alter table hr_assessment_task_dtl modify update_by varchar(24);
alter table hr_assessment_task_dtl modify delete_by varchar(24);
alter table hr_assessment_task_dtl modify tenant_id varchar(24);
alter table hr_assessment_tpl modify id varchar(24);
alter table hr_assessment_tpl modify create_by varchar(24);
alter table hr_assessment_tpl modify update_by varchar(24);
alter table hr_assessment_tpl modify delete_by varchar(24);
alter table hr_assessment_tpl modify tenant_id varchar(24);
alter table hr_attendance_data modify id varchar(24);
alter table hr_attendance_data modify create_by varchar(24);
alter table hr_attendance_data modify update_by varchar(24);
alter table hr_attendance_data modify delete_by varchar(24);
alter table hr_attendance_data modify tenant_id varchar(24);
alter table hr_attendance_data_process modify id varchar(24);
alter table hr_attendance_data_process modify create_by varchar(24);
alter table hr_attendance_data_process modify update_by varchar(24);
alter table hr_attendance_data_process modify delete_by varchar(24);
alter table hr_attendance_date modify id varchar(24);
alter table hr_attendance_date modify create_by varchar(24);
alter table hr_attendance_date modify update_by varchar(24);
alter table hr_attendance_date modify delete_by varchar(24);
alter table hr_attendance_date modify tenant_id varchar(24);
alter table hr_attendance_holiday modify id varchar(24);
alter table hr_attendance_holiday modify create_by varchar(24);
alter table hr_attendance_holiday modify update_by varchar(24);
alter table hr_attendance_holiday modify delete_by varchar(24);
alter table hr_attendance_holiday modify tenant_id varchar(24);
alter table hr_attendance_official_busi modify id varchar(24);
alter table hr_attendance_official_busi modify create_by varchar(24);
alter table hr_attendance_official_busi modify update_by varchar(24);
alter table hr_attendance_official_busi modify delete_by varchar(24);
alter table hr_attendance_official_busi modify tenant_id varchar(24);
alter table hr_attendance_overtime modify id varchar(24);
alter table hr_attendance_overtime modify create_by varchar(24);
alter table hr_attendance_overtime modify update_by varchar(24);
alter table hr_attendance_overtime modify delete_by varchar(24);
alter table hr_attendance_overtime modify tenant_id varchar(24);
alter table hr_attendance_process modify id varchar(24);
alter table hr_attendance_process modify create_by varchar(24);
alter table hr_attendance_process modify update_by varchar(24);
alter table hr_attendance_process modify delete_by varchar(24);
alter table hr_attendance_process modify tenant_id varchar(24);
alter table hr_attendance_record modify id varchar(24);
alter table hr_attendance_record modify create_by varchar(24);
alter table hr_attendance_record modify update_by varchar(24);
alter table hr_attendance_record modify delete_by varchar(24);
alter table hr_attendance_record modify tenant_id varchar(24);
alter table hr_attendance_record_import modify id varchar(24);
alter table hr_attendance_record_import modify create_by varchar(24);
alter table hr_attendance_record_import modify update_by varchar(24);
alter table hr_attendance_record_import modify delete_by varchar(24);
alter table hr_attendance_record_import modify tenant_id varchar(24);
alter table hr_attendance_tpl modify id varchar(24);
alter table hr_attendance_tpl modify create_by varchar(24);
alter table hr_attendance_tpl modify update_by varchar(24);
alter table hr_attendance_tpl modify delete_by varchar(24);
alter table hr_attendance_tpl modify tenant_id varchar(24);
alter table hr_attendance_tpl_dtl modify id varchar(24);
alter table hr_attendance_tpl_dtl modify create_by varchar(24);
alter table hr_attendance_tpl_dtl modify update_by varchar(24);
alter table hr_attendance_tpl_dtl modify delete_by varchar(24);
alter table hr_attendance_tpl_group modify id varchar(24);
alter table hr_attendance_tpl_group modify create_by varchar(24);
alter table hr_attendance_tpl_group modify update_by varchar(24);
alter table hr_attendance_tpl_group modify delete_by varchar(24);
alter table hr_attendance_tpl_group modify tenant_id varchar(24);
alter table hr_attendance_type modify create_by varchar(24);
alter table hr_attendance_type modify update_by varchar(24);
alter table hr_attendance_type modify delete_by varchar(24);
alter table hr_attendance_type modify tenant_id varchar(24);
alter table hr_attendance_year_day modify create_by varchar(24);
alter table hr_attendance_year_day modify update_by varchar(24);
alter table hr_attendance_year_day modify delete_by varchar(24);
alter table hr_attendance_year_day modify tenant_id varchar(24);
alter table hr_attendance_year_rule modify id varchar(24);
alter table hr_attendance_year_rule modify create_by varchar(24);
alter table hr_attendance_year_rule modify update_by varchar(24);
alter table hr_attendance_year_rule modify delete_by varchar(24);
alter table hr_attendance_year_rule modify tenant_id varchar(24);
alter table hr_business_travel_data modify id varchar(24);
alter table hr_business_travel_data modify create_by varchar(24);
alter table hr_business_travel_data modify update_by varchar(24);
alter table hr_business_travel_data modify delete_by varchar(24);
alter table hr_business_travel_data modify tenant_id varchar(24);
alter table hr_certificate modify create_by varchar(24);
alter table hr_certificate modify update_by varchar(24);
alter table hr_certificate modify delete_by varchar(24);
alter table hr_certificate modify tenant_id varchar(24);
alter table hr_certificate_level modify create_by varchar(24);
alter table hr_certificate_level modify update_by varchar(24);
alter table hr_certificate_level modify delete_by varchar(24);
alter table hr_certificate_level modify tenant_id varchar(24);
alter table hr_certificate_type modify create_by varchar(24);
alter table hr_certificate_type modify update_by varchar(24);
alter table hr_certificate_type modify delete_by varchar(24);
alter table hr_certificate_type modify tenant_id varchar(24);
alter table hr_contract_org modify create_by varchar(24);
alter table hr_contract_org modify update_by varchar(24);
alter table hr_contract_org modify delete_by varchar(24);
alter table hr_contract_org modify tenant_id varchar(24);
alter table hr_employee_transfer modify in_org_id varchar(24);
alter table hr_employee_transfer modify pos_id varchar(24);
alter table hr_employee_transfer modify create_by varchar(24);
alter table hr_employee_transfer modify update_by varchar(24);
alter table hr_employee_transfer modify delete_by varchar(24);
alter table hr_employee_transfer modify tenant_id varchar(24);
alter table hr_evaluation_relation modify create_by varchar(24);
alter table hr_evaluation_relation modify update_by varchar(24);
alter table hr_evaluation_relation modify delete_by varchar(24);
alter table hr_evaluation_relation modify tenant_id varchar(24);
alter table hr_indicator_lib modify id varchar(24);
alter table hr_indicator_lib modify create_by varchar(24);
alter table hr_indicator_lib modify update_by varchar(24);
alter table hr_indicator_lib modify delete_by varchar(24);
alter table hr_indicator_lib modify tenant_id varchar(24);
alter table hr_interview modify create_by varchar(24);
alter table hr_interview modify update_by varchar(24);
alter table hr_interview modify delete_by varchar(24);
alter table hr_interview modify tenant_id varchar(24);
alter table hr_learn modify create_by varchar(24);
alter table hr_learn modify update_by varchar(24);
alter table hr_learn modify delete_by varchar(24);
alter table hr_learn modify tenant_id varchar(24);
alter table hr_learn_paper modify create_by varchar(24);
alter table hr_learn_paper modify update_by varchar(24);
alter table hr_learn_paper modify delete_by varchar(24);
alter table hr_learn_paper modify tenant_id varchar(24);
alter table hr_learn_release modify create_by varchar(24);
alter table hr_learn_release modify update_by varchar(24);
alter table hr_learn_release modify delete_by varchar(24);
alter table hr_learn_release modify tenant_id varchar(24);
alter table hr_learn_task modify create_by varchar(24);
alter table hr_learn_task modify update_by varchar(24);
alter table hr_learn_task modify delete_by varchar(24);
alter table hr_learn_task modify tenant_id varchar(24);
alter table hr_paper modify create_by varchar(24);
alter table hr_paper modify update_by varchar(24);
alter table hr_paper modify delete_by varchar(24);
alter table hr_paper modify tenant_id varchar(24);
alter table hr_paper_learn modify create_by varchar(24);
alter table hr_paper_learn modify update_by varchar(24);
alter table hr_paper_learn modify delete_by varchar(24);
alter table hr_paper_learn modify tenant_id varchar(24);
alter table hr_paper_question modify create_by varchar(24);
alter table hr_paper_question modify update_by varchar(24);
alter table hr_paper_question modify delete_by varchar(24);
alter table hr_paper_question_answer modify create_by varchar(24);
alter table hr_paper_question_answer modify update_by varchar(24);
alter table hr_paper_question_answer modify delete_by varchar(24);
alter table hr_paper_question_item modify create_by varchar(24);
alter table hr_paper_question_item modify update_by varchar(24);
alter table hr_paper_question_item modify delete_by varchar(24);
alter table hr_paper_question_rel modify create_by varchar(24);
alter table hr_paper_question_rel modify update_by varchar(24);
alter table hr_paper_question_rel modify delete_by varchar(24);
alter table hr_paper_release modify create_by varchar(24);
alter table hr_paper_release modify update_by varchar(24);
alter table hr_paper_release modify delete_by varchar(24);
alter table hr_paper_task modify create_by varchar(24);
alter table hr_paper_task modify update_by varchar(24);
alter table hr_paper_task modify delete_by varchar(24);
alter table hr_paper_task modify tenant_id varchar(24);
alter table hr_paper_task_achievement modify create_by varchar(24);
alter table hr_paper_task_achievement modify update_by varchar(24);
alter table hr_paper_task_achievement modify delete_by varchar(24);
alter table hr_paper_task_achievement modify tenant_id varchar(24);
alter table hr_performance_level modify create_by varchar(24);
alter table hr_performance_level modify update_by varchar(24);
alter table hr_performance_level modify delete_by varchar(24);
alter table hr_performance_level modify tenant_id varchar(24);
alter table hr_performance_rcd modify create_by varchar(24);
alter table hr_performance_rcd modify update_by varchar(24);
alter table hr_performance_rcd modify delete_by varchar(24);
alter table hr_performance_rcd modify tenant_id varchar(24);
alter table hr_person modify create_by varchar(24);
alter table hr_person modify update_by varchar(24);
alter table hr_person modify delete_by varchar(24);
alter table hr_person modify tenant_id varchar(24);
alter table hr_person_absence_apply modify create_by varchar(24);
alter table hr_person_absence_apply modify update_by varchar(24);
alter table hr_person_absence_apply modify delete_by varchar(24);
alter table hr_person_absence_apply modify tenant_id varchar(24);
alter table hr_person_action_record modify create_by varchar(24);
alter table hr_person_action_record modify update_by varchar(24);
alter table hr_person_action_record modify delete_by varchar(24);
alter table hr_person_action_record modify tenant_id varchar(24);
alter table hr_person_approval_rcd modify id varchar(24);
alter table hr_person_approval_rcd modify create_by varchar(24);
alter table hr_person_approval_rcd modify update_by varchar(24);
alter table hr_person_approval_rcd modify delete_by varchar(24);
alter table hr_person_approval_rcd modify tenant_id varchar(24);
alter table hr_person_attendance modify id varchar(24);
alter table hr_person_attendance modify create_by varchar(24);
alter table hr_person_attendance modify update_by varchar(24);
alter table hr_person_attendance modify delete_by varchar(24);
alter table hr_person_attendance modify tenant_id varchar(24);
alter table hr_person_attendance_money modify id varchar(24);
alter table hr_person_attendance_money modify create_by varchar(24);
alter table hr_person_attendance_money modify update_by varchar(24);
alter table hr_person_attendance_money modify delete_by varchar(24);
alter table hr_person_attendance_money modify tenant_id varchar(24);
alter table hr_person_attendance_rec modify id varchar(24);
alter table hr_person_attendance_rec modify create_by varchar(24);
alter table hr_person_attendance_rec modify update_by varchar(24);
alter table hr_person_attendance_rec modify delete_by varchar(24);
alter table hr_person_attendance_rec modify tenant_id varchar(24);
alter table hr_person_busi_insure modify create_by varchar(24);
alter table hr_person_busi_insure modify update_by varchar(24);
alter table hr_person_busi_insure modify delete_by varchar(24);
alter table hr_person_busi_insure modify tenant_id varchar(24);
alter table hr_person_busi_insure_type modify create_by varchar(24);
alter table hr_person_busi_insure_type modify update_by varchar(24);
alter table hr_person_busi_insure_type modify delete_by varchar(24);
alter table hr_person_busi_insure_type modify tenant_id varchar(24);
alter table hr_person_cert modify create_by varchar(24);
alter table hr_person_cert modify update_by varchar(24);
alter table hr_person_cert modify delete_by varchar(24);
alter table hr_person_cert modify tenant_id varchar(24);
alter table hr_person_certificate modify id varchar(24);
alter table hr_person_certificate modify create_by varchar(24);
alter table hr_person_certificate modify update_by varchar(24);
alter table hr_person_certificate modify delete_by varchar(24);
alter table hr_person_certificate modify tenant_id varchar(24);
alter table hr_person_certificate2 modify id varchar(24);
alter table hr_person_certificate2 modify create_by varchar(24);
alter table hr_person_certificate2 modify update_by varchar(24);
alter table hr_person_certificate2 modify delete_by varchar(24);
alter table hr_person_certificate2 modify tenant_id varchar(24);
alter table hr_person_change_rcd modify create_by varchar(24);
alter table hr_person_change_rcd modify update_by varchar(24);
alter table hr_person_change_rcd modify delete_by varchar(24);
alter table hr_person_confirm_apply modify create_by varchar(24);
alter table hr_person_confirm_apply modify update_by varchar(24);
alter table hr_person_confirm_apply modify delete_by varchar(24);
alter table hr_person_confirm_apply modify tenant_id varchar(24);
alter table hr_person_contract modify id varchar(24);
alter table hr_person_contract modify create_by varchar(24);
alter table hr_person_contract modify update_by varchar(24);
alter table hr_person_contract modify delete_by varchar(24);
alter table hr_person_contract modify tenant_id varchar(24);
alter table hr_person_education modify id varchar(24);
alter table hr_person_education modify create_by varchar(24);
alter table hr_person_education modify update_by varchar(24);
alter table hr_person_education modify delete_by varchar(24);
alter table hr_person_education modify tenant_id varchar(24);
alter table hr_person_file modify create_by varchar(24);
alter table hr_person_file modify update_by varchar(24);
alter table hr_person_file modify delete_by varchar(24);
alter table hr_person_file modify tenant_id varchar(24);
alter table hr_person_file_org modify create_by varchar(24);
alter table hr_person_file_org modify update_by varchar(24);
alter table hr_person_file_org modify delete_by varchar(24);
alter table hr_person_file_org modify tenant_id varchar(24);
alter table hr_person_file_out modify id varchar(24);
alter table hr_person_file_out modify create_by varchar(24);
alter table hr_person_file_out modify update_by varchar(24);
alter table hr_person_file_out modify delete_by varchar(24);
alter table hr_person_formal_act modify create_by varchar(24);
alter table hr_person_formal_act modify update_by varchar(24);
alter table hr_person_formal_act modify delete_by varchar(24);
alter table hr_person_formal_act modify tenant_id varchar(24);
alter table hr_person_income_certificate_apply modify create_by varchar(24);
alter table hr_person_income_certificate_apply modify update_by varchar(24);
alter table hr_person_income_certificate_apply modify delete_by varchar(24);
alter table hr_person_income_certificate_apply modify tenant_id varchar(24);
alter table hr_person_interview modify create_by varchar(24);
alter table hr_person_interview modify update_by varchar(24);
alter table hr_person_interview modify delete_by varchar(24);
alter table hr_person_interview modify tenant_id varchar(24);
alter table hr_person_leave modify create_by varchar(24);
alter table hr_person_leave modify update_by varchar(24);
alter table hr_person_leave modify delete_by varchar(24);
alter table hr_person_leave modify tenant_id varchar(24);
alter table hr_person_leave_act modify create_by varchar(24);
alter table hr_person_leave_act modify update_by varchar(24);
alter table hr_person_leave_act modify delete_by varchar(24);
alter table hr_person_leave_act modify tenant_id varchar(24);
alter table hr_person_official_business modify create_by varchar(24);
alter table hr_person_official_business modify update_by varchar(24);
alter table hr_person_official_business modify delete_by varchar(24);
alter table hr_person_official_business modify tenant_id varchar(24);
alter table hr_person_professional modify id varchar(24);
alter table hr_person_professional modify create_by varchar(24);
alter table hr_person_professional modify update_by varchar(24);
alter table hr_person_professional modify delete_by varchar(24);
alter table hr_person_professional modify tenant_id varchar(24);
alter table hr_person_record modify create_by varchar(24);
alter table hr_person_record modify update_by varchar(24);
alter table hr_person_record modify delete_by varchar(24);
alter table hr_person_record modify tenant_id varchar(24);
alter table hr_person_resignation modify create_by varchar(24);
alter table hr_person_resignation modify update_by varchar(24);
alter table hr_person_resignation modify delete_by varchar(24);
alter table hr_person_resignation modify tenant_id varchar(24);
alter table hr_person_resume modify id varchar(24);
alter table hr_person_resume modify create_by varchar(24);
alter table hr_person_resume modify update_by varchar(24);
alter table hr_person_resume modify delete_by varchar(24);
alter table hr_person_retire_act modify create_by varchar(24);
alter table hr_person_retire_act modify update_by varchar(24);
alter table hr_person_retire_act modify delete_by varchar(24);
alter table hr_person_retire_act modify tenant_id varchar(24);
alter table hr_person_score modify create_by varchar(24);
alter table hr_person_score modify update_by varchar(24);
alter table hr_person_score modify delete_by varchar(24);
alter table hr_person_secondment modify create_by varchar(24);
alter table hr_person_secondment modify update_by varchar(24);
alter table hr_person_secondment modify delete_by varchar(24);
alter table hr_person_secondment modify tenant_id varchar(24);
alter table hr_person_social_relation modify id varchar(24);
alter table hr_person_social_relation modify create_by varchar(24);
alter table hr_person_social_relation modify update_by varchar(24);
alter table hr_person_social_relation modify delete_by varchar(24);
alter table hr_person_social_relation modify tenant_id varchar(24);
alter table hr_person_store modify create_by varchar(24);
alter table hr_person_store modify update_by varchar(24);
alter table hr_person_store modify delete_by varchar(24);
alter table hr_person_store modify tenant_id varchar(24);
alter table hr_person_transfer modify create_by varchar(24);
alter table hr_person_transfer modify update_by varchar(24);
alter table hr_person_transfer modify delete_by varchar(24);
alter table hr_person_transfer modify tenant_id varchar(24);
alter table hr_person_transfer_act modify create_by varchar(24);
alter table hr_person_transfer_act modify update_by varchar(24);
alter table hr_person_transfer_act modify delete_by varchar(24);
alter table hr_person_transfer_rcd modify create_by varchar(24);
alter table hr_person_transfer_rcd modify update_by varchar(24);
alter table hr_person_transfer_rcd modify delete_by varchar(24);
alter table hr_person_work_experience modify id varchar(24);
alter table hr_person_work_experience modify create_by varchar(24);
alter table hr_person_work_experience modify update_by varchar(24);
alter table hr_person_work_experience modify delete_by varchar(24);
alter table hr_person_work_experience modify tenant_id varchar(24);
alter table hr_personnel_requirement_apply modify id varchar(24);
alter table hr_personnel_requirement_apply modify create_by varchar(24);
alter table hr_personnel_requirement_apply modify update_by varchar(24);
alter table hr_personnel_requirement_apply modify delete_by varchar(24);
alter table hr_personnel_requirement_apply modify tenant_id varchar(24);
alter table hr_position_info modify create_by varchar(24);
alter table hr_position_info modify update_by varchar(24);
alter table hr_position_info modify delete_by varchar(24);
alter table hr_position_info modify tenant_id varchar(24);
alter table hr_position_type modify create_by varchar(24);
alter table hr_position_type modify update_by varchar(24);
alter table hr_position_type modify delete_by varchar(24);
alter table hr_position_type modify tenant_id varchar(24);
alter table hr_professional_level modify create_by varchar(24);
alter table hr_professional_level modify update_by varchar(24);
alter table hr_professional_level modify delete_by varchar(24);
alter table hr_professional_level modify tenant_id varchar(24);
alter table hr_rank modify create_by varchar(24);
alter table hr_rank modify update_by varchar(24);
alter table hr_rank modify delete_by varchar(24);
alter table hr_rank modify tenant_id varchar(24);
alter table hr_recruit_person_rec modify create_by varchar(24);
alter table hr_recruit_person_rec modify update_by varchar(24);
alter table hr_recruit_person_rec modify delete_by varchar(24);
alter table hr_recruit_person_rec modify tenant_id varchar(24);
alter table hr_recruit_post_rec modify create_by varchar(24);
alter table hr_recruit_post_rec modify update_by varchar(24);
alter table hr_recruit_post_rec modify delete_by varchar(24);
alter table hr_recruit_post_rec modify tenant_id varchar(24);
alter table hr_recruit_record modify create_by varchar(24);
alter table hr_recruit_record modify update_by varchar(24);
alter table hr_recruit_record modify delete_by varchar(24);
alter table hr_recruit_record modify tenant_id varchar(24);
alter table hr_recruitment_plan_apply modify id varchar(24);
alter table hr_recruitment_plan_apply modify create_by varchar(24);
alter table hr_recruitment_plan_apply modify update_by varchar(24);
alter table hr_recruitment_plan_apply modify delete_by varchar(24);
alter table hr_recruitment_plan_apply modify tenant_id varchar(24);
alter table hr_salary modify id varchar(24);
alter table hr_salary modify create_by varchar(24);
alter table hr_salary modify update_by varchar(24);
alter table hr_salary modify delete_by varchar(24);
alter table hr_salary modify tenant_id varchar(24);
alter table hr_salary_action modify id varchar(24);
alter table hr_salary_action modify create_by varchar(24);
alter table hr_salary_action modify update_by varchar(24);
alter table hr_salary_action modify delete_by varchar(24);
alter table hr_salary_action modify tenant_id varchar(24);
alter table hr_salary_column modify id varchar(24);
alter table hr_salary_column modify create_by varchar(24);
alter table hr_salary_column modify update_by varchar(24);
alter table hr_salary_column modify delete_by varchar(24);
alter table hr_salary_column modify tenant_id varchar(24);
alter table hr_salary_ctl modify create_by varchar(24);
alter table hr_salary_ctl modify update_by varchar(24);
alter table hr_salary_ctl modify delete_by varchar(24);
alter table hr_salary_ctl modify tenant_id varchar(24);
alter table hr_salary_detail modify id varchar(24);
alter table hr_salary_detail modify create_by varchar(24);
alter table hr_salary_detail modify update_by varchar(24);
alter table hr_salary_detail modify delete_by varchar(24);
alter table hr_salary_detail modify tenant_id varchar(24);
alter table hr_salary_income_tax modify id varchar(24);
alter table hr_salary_income_tax modify create_by varchar(24);
alter table hr_salary_income_tax modify update_by varchar(24);
alter table hr_salary_income_tax modify delete_by varchar(24);
alter table hr_salary_income_tax modify tenant_id varchar(24);
alter table hr_salary_month modify id varchar(24);
alter table hr_salary_month modify create_by varchar(24);
alter table hr_salary_month modify update_by varchar(24);
alter table hr_salary_month modify delete_by varchar(24);
alter table hr_salary_month modify tenant_id varchar(24);
alter table hr_salary_project_commission modify id varchar(24);
alter table hr_salary_project_commission modify create_by varchar(24);
alter table hr_salary_project_commission modify update_by varchar(24);
alter table hr_salary_project_commission modify delete_by varchar(24);
alter table hr_salary_project_commission modify tenant_id varchar(24);
alter table hr_salary_project_commission_rcd modify id varchar(24);
alter table hr_salary_project_commission_rcd modify create_by varchar(24);
alter table hr_salary_project_commission_rcd modify update_by varchar(24);
alter table hr_salary_project_commission_rcd modify delete_by varchar(24);
alter table hr_salary_project_commission_rcd modify tenant_id varchar(24);
alter table hr_salary_project_time modify id varchar(24);
alter table hr_salary_project_time modify create_by varchar(24);
alter table hr_salary_project_time modify update_by varchar(24);
alter table hr_salary_project_time modify delete_by varchar(24);
alter table hr_salary_project_time modify tenant_id varchar(24);
alter table hr_salary_project_time_rcd modify id varchar(24);
alter table hr_salary_project_time_rcd modify create_by varchar(24);
alter table hr_salary_project_time_rcd modify update_by varchar(24);
alter table hr_salary_project_time_rcd modify delete_by varchar(24);
alter table hr_salary_project_time_rcd modify tenant_id varchar(24);
alter table hr_salary_project_unit modify id varchar(24);
alter table hr_salary_project_unit modify create_by varchar(24);
alter table hr_salary_project_unit modify update_by varchar(24);
alter table hr_salary_project_unit modify delete_by varchar(24);
alter table hr_salary_project_unit modify tenant_id varchar(24);
alter table hr_salary_project_unit_rcd modify id varchar(24);
alter table hr_salary_project_unit_rcd modify create_by varchar(24);
alter table hr_salary_project_unit_rcd modify update_by varchar(24);
alter table hr_salary_project_unit_rcd modify delete_by varchar(24);
alter table hr_salary_project_unit_rcd modify tenant_id varchar(24);
alter table hr_salary_tax_pct modify id varchar(24);
alter table hr_salary_tax_pct modify tenant_id varchar(24);
alter table hr_salary_tpl modify create_by varchar(24);
alter table hr_salary_tpl modify update_by varchar(24);
alter table hr_salary_tpl modify delete_by varchar(24);
alter table hr_salary_tpl modify tenant_id varchar(24);
alter table hr_salary_tpl_item modify create_by varchar(24);
alter table hr_salary_tpl_item modify update_by varchar(24);
alter table hr_salary_tpl_item modify delete_by varchar(24);
alter table hr_salary_tpl_item modify tenant_id varchar(24);
alter table hr_salary_tpl_person modify create_by varchar(24);
alter table hr_salary_tpl_person modify update_by varchar(24);
alter table hr_salary_tpl_person modify delete_by varchar(24);
alter table hr_salary_tpl_person modify tenant_id varchar(24);
alter table hr_score_indicator modify notes varchar(24);
alter table hr_score_indicator modify create_by varchar(24);
alter table hr_score_indicator modify update_by varchar(24);
alter table hr_score_indicator modify delete_by varchar(24);
alter table hr_score_indicator modify tenant_id varchar(24);
alter table hr_score_task modify notes varchar(24);
alter table hr_score_task modify create_by varchar(24);
alter table hr_score_task modify update_by varchar(24);
alter table hr_score_task modify delete_by varchar(24);
alter table hr_score_task modify tenant_id varchar(24);
alter table hr_score_task_conf modify notes varchar(24);
alter table hr_score_task_conf modify create_by varchar(24);
alter table hr_score_task_conf modify update_by varchar(24);
alter table hr_score_task_conf modify delete_by varchar(24);
alter table hr_score_task_conf modify tenant_id varchar(24);
alter table hr_score_task_dtl modify notes varchar(24);
alter table hr_score_task_dtl modify create_by varchar(24);
alter table hr_score_task_dtl modify update_by varchar(24);
alter table hr_score_task_dtl modify delete_by varchar(24);
alter table hr_score_task_dtl modify tenant_id varchar(24);
alter table hr_score_task_inst modify notes varchar(24);
alter table hr_score_task_inst modify create_by varchar(24);
alter table hr_score_task_inst modify update_by varchar(24);
alter table hr_score_task_inst modify delete_by varchar(24);
alter table hr_score_task_org modify notes varchar(24);
alter table hr_score_task_org modify create_by varchar(24);
alter table hr_score_task_org modify update_by varchar(24);
alter table hr_score_task_org modify delete_by varchar(24);
alter table hr_score_task_user modify notes varchar(24);
alter table hr_score_task_user modify create_by varchar(24);
alter table hr_score_task_user modify update_by varchar(24);
alter table hr_score_task_user modify delete_by varchar(24);
alter table hr_score_tpl modify create_by varchar(24);
alter table hr_score_tpl modify update_by varchar(24);
alter table hr_score_tpl modify delete_by varchar(24);
alter table hr_score_tpl modify tenant_id varchar(24);
alter table hr_score_tpl_item modify notes varchar(24);
alter table hr_score_tpl_item modify create_by varchar(24);
alter table hr_score_tpl_item modify update_by varchar(24);
alter table hr_score_tpl_item modify delete_by varchar(24);
alter table hr_score_tpl_item modify tenant_id varchar(24);
alter table hr_score_tpl_item_value modify notes varchar(24);
alter table hr_score_tpl_item_value modify create_by varchar(24);
alter table hr_score_tpl_item_value modify update_by varchar(24);
alter table hr_score_tpl_item_value modify delete_by varchar(24);
alter table hr_score_tpl_item_value modify tenant_id varchar(24);
alter table hr_score_user modify notes varchar(24);
alter table hr_score_user modify create_by varchar(24);
alter table hr_score_user modify update_by varchar(24);
alter table hr_score_user modify delete_by varchar(24);
alter table hr_training_institution modify id varchar(24);
alter table hr_training_institution modify create_by varchar(24);
alter table hr_training_institution modify update_by varchar(24);
alter table hr_training_institution modify delete_by varchar(24);
alter table hr_training_institution modify tenant_id varchar(24);
alter table hr_training_instructor modify id varchar(24);
alter table hr_training_instructor modify create_by varchar(24);
alter table hr_training_instructor modify update_by varchar(24);
alter table hr_training_instructor modify delete_by varchar(24);
alter table hr_training_instructor modify tenant_id varchar(24);
alter table hr_vacation_data modify id varchar(24);
alter table hr_vacation_data modify create_by varchar(24);
alter table hr_vacation_data modify update_by varchar(24);
alter table hr_vacation_data modify delete_by varchar(24);
alter table hr_vacation_data modify tenant_id varchar(24);
alter table hr_work_overtime_data modify id varchar(24);
alter table hr_work_overtime_data modify create_by varchar(24);
alter table hr_work_overtime_data modify update_by varchar(24);
alter table hr_work_overtime_data modify delete_by varchar(24);
alter table hr_work_overtime_data modify tenant_id varchar(24);
alter table hrm_company modify id varchar(24);
alter table hrm_company modify create_by varchar(24);
alter table hrm_company modify update_by varchar(24);
alter table hrm_company modify delete_by varchar(24);
alter table hrm_employee modify person_id varchar(24);
alter table hrm_employee modify company_id varchar(24);
alter table hrm_employee modify tenant_id varchar(24);
alter table hrm_employee modify create_by varchar(24);
alter table hrm_employee modify update_by varchar(24);
alter table hrm_employee modify delete_by varchar(24);
alter table hrm_employee modify direct_leader_id varchar(24);
alter table hrm_employee_position modify id varchar(24);
alter table hrm_employee_position modify employee_id varchar(24);
alter table hrm_employee_position modify position_id varchar(24);
alter table hrm_employee_position modify create_by varchar(24);
alter table hrm_employee_position modify update_by varchar(24);
alter table hrm_employee_position modify delete_by varchar(24);
alter table hrm_favourite_group modify id varchar(24);
alter table hrm_favourite_group modify parent_id varchar(24);
alter table hrm_favourite_group modify employee_id varchar(24);
alter table hrm_favourite_group modify company_id varchar(24);
alter table hrm_favourite_group modify tenant_id varchar(24);
alter table hrm_favourite_group modify create_by varchar(24);
alter table hrm_favourite_group modify update_by varchar(24);
alter table hrm_favourite_group modify delete_by varchar(24);
alter table hrm_favourite_group_item modify id varchar(24);
alter table hrm_favourite_group_item modify target_id varchar(24);
alter table hrm_favourite_group_item modify target_type varchar(24);
alter table hrm_favourite_group_item modify employee_id varchar(24);
alter table hrm_favourite_group_item modify company_id varchar(24);
alter table hrm_favourite_group_item modify tenant_id varchar(24);
alter table hrm_favourite_group_item modify create_by varchar(24);
alter table hrm_favourite_group_item modify update_by varchar(24);
alter table hrm_favourite_group_item modify delete_by varchar(24);
alter table hrm_organization modify id varchar(24);
alter table hrm_organization modify type varchar(24);
alter table hrm_organization modify parent_id varchar(24);
alter table hrm_organization modify company_id varchar(24);
alter table hrm_organization modify tenant_id varchar(24);
alter table hrm_organization modify create_by varchar(24);
alter table hrm_organization modify update_by varchar(24);
alter table hrm_organization modify delete_by varchar(24);
alter table hrm_person modify id varchar(24);
alter table hrm_person modify create_by varchar(24);
alter table hrm_person modify update_by varchar(24);
alter table hrm_person modify delete_by varchar(24);
alter table hrm_position modify id varchar(24);
alter table hrm_position modify org_id varchar(24);
alter table hrm_position modify company_id varchar(24);
alter table hrm_position modify tenant_id varchar(24);
alter table hrm_position modify create_by varchar(24);
alter table hrm_position modify update_by varchar(24);
alter table hrm_position modify delete_by varchar(24);
alter table hrm_position modify type varchar(24);
alter table iot_equipment modify create_by varchar(24);
alter table iot_equipment modify update_by varchar(24);
alter table iot_equipment modify delete_by varchar(24);
alter table iot_equipment modify tenant_id varchar(24);
alter table iot_equipment_label modify create_by varchar(24);
alter table iot_equipment_label modify update_by varchar(24);
alter table iot_equipment_label modify delete_by varchar(24);
alter table iot_equipment_log modify create_by varchar(24);
alter table iot_equipment_log modify update_by varchar(24);
alter table iot_equipment_log modify delete_by varchar(24);
alter table iot_product modify create_by varchar(24);
alter table iot_product modify update_by varchar(24);
alter table iot_product modify delete_by varchar(24);
alter table iot_product modify tenant_id varchar(24);
alter table iot_product_content modify create_by varchar(24);
alter table iot_product_content modify update_by varchar(24);
alter table iot_product_content modify delete_by varchar(24);
alter table iot_product_content modify tenant_id varchar(24);
alter table iot_product_function modify create_by varchar(24);
alter table iot_product_function modify update_by varchar(24);
alter table iot_product_function modify delete_by varchar(24);
alter table iot_product_function modify tenant_id varchar(24);
alter table iot_product_function_item modify create_by varchar(24);
alter table iot_product_function_item modify update_by varchar(24);
alter table iot_product_function_item modify delete_by varchar(24);
alter table iot_virt_equipment modify create_by varchar(24);
alter table iot_virt_equipment modify update_by varchar(24);
alter table iot_virt_equipment modify delete_by varchar(24);
alter table iot_virt_equipment modify tenant_id varchar(24);
alter table kn_category modify create_by varchar(24);
alter table kn_category modify update_by varchar(24);
alter table kn_category modify delete_by varchar(24);
alter table kn_category modify tenant_id varchar(24);
alter table kn_content modify create_by varchar(24);
alter table kn_content modify update_by varchar(24);
alter table kn_content modify delete_by varchar(24);
alter table kn_content modify tenant_id varchar(24);
alter table oa_banner modify create_by varchar(24);
alter table oa_banner modify update_by varchar(24);
alter table oa_banner modify delete_by varchar(24);
alter table oa_banner modify tenant_id varchar(24);
alter table oa_crm_customer modify create_by varchar(24);
alter table oa_crm_customer modify update_by varchar(24);
alter table oa_crm_customer modify delete_by varchar(24);
alter table oa_crm_customer modify tenant_id varchar(24);
alter table oa_crm_customer_business modify create_by varchar(24);
alter table oa_crm_customer_business modify update_by varchar(24);
alter table oa_crm_customer_business modify delete_by varchar(24);
alter table oa_crm_customer_business modify tenant_id varchar(24);
alter table oa_crm_customer_care modify create_by varchar(24);
alter table oa_crm_customer_care modify update_by varchar(24);
alter table oa_crm_customer_care modify delete_by varchar(24);
alter table oa_crm_customer_contact modify create_by varchar(24);
alter table oa_crm_customer_contact modify update_by varchar(24);
alter table oa_crm_customer_contact modify delete_by varchar(24);
alter table oa_crm_customer_contact modify tenant_id varchar(24);
alter table oa_crm_customer_follow modify create_by varchar(24);
alter table oa_crm_customer_follow modify update_by varchar(24);
alter table oa_crm_customer_follow modify delete_by varchar(24);
alter table oa_crm_customer_follow modify tenant_id varchar(24);
alter table oa_crm_customer_industry modify create_by varchar(24);
alter table oa_crm_customer_industry modify update_by varchar(24);
alter table oa_crm_customer_industry modify delete_by varchar(24);
alter table oa_crm_customer_industry modify tenant_id varchar(24);
alter table oa_crm_customer_intentional modify create_by varchar(24);
alter table oa_crm_customer_intentional modify update_by varchar(24);
alter table oa_crm_customer_intentional modify delete_by varchar(24);
alter table oa_crm_customer_intentional modify tenant_id varchar(24);
alter table oa_crm_customer_leader modify create_by varchar(24);
alter table oa_crm_customer_leader modify update_by varchar(24);
alter table oa_crm_customer_leader modify delete_by varchar(24);
alter table oa_crm_customer_leader modify tenant_id varchar(24);
alter table oa_crm_customer_leads modify create_by varchar(24);
alter table oa_crm_customer_leads modify update_by varchar(24);
alter table oa_crm_customer_leads modify delete_by varchar(24);
alter table oa_crm_customer_leads modify tenant_id varchar(24);
alter table oa_crm_customer_level modify create_by varchar(24);
alter table oa_crm_customer_level modify update_by varchar(24);
alter table oa_crm_customer_level modify delete_by varchar(24);
alter table oa_crm_customer_level modify tenant_id varchar(24);
alter table oa_crm_customer_record modify create_by varchar(24);
alter table oa_crm_customer_record modify update_by varchar(24);
alter table oa_crm_customer_record modify delete_by varchar(24);
alter table oa_crm_customer_review modify create_by varchar(24);
alter table oa_crm_customer_review modify update_by varchar(24);
alter table oa_crm_customer_review modify delete_by varchar(24);
alter table oa_crm_customer_review modify tenant_id varchar(24);
alter table oa_crm_customer_source modify create_by varchar(24);
alter table oa_crm_customer_source modify update_by varchar(24);
alter table oa_crm_customer_source modify delete_by varchar(24);
alter table oa_crm_customer_source modify tenant_id varchar(24);
alter table oa_crm_customer_team modify create_by varchar(24);
alter table oa_crm_customer_team modify update_by varchar(24);
alter table oa_crm_customer_team modify delete_by varchar(24);
alter table oa_crm_customer_team modify tenant_id varchar(24);
alter table oa_download_file modify create_by varchar(24);
alter table oa_download_file modify update_by varchar(24);
alter table oa_download_file modify delete_by varchar(24);
alter table oa_download_file modify tenant_id varchar(24);
alter table oa_download_log modify create_by varchar(24);
alter table oa_download_log modify update_by varchar(24);
alter table oa_download_log modify delete_by varchar(24);
alter table oa_fin_accounting_record modify create_by varchar(24);
alter table oa_fin_accounting_record modify update_by varchar(24);
alter table oa_fin_accounting_record modify delete_by varchar(24);
alter table oa_fin_accounting_record modify tenant_id varchar(24);
alter table oa_fin_category modify create_by varchar(24);
alter table oa_fin_category modify update_by varchar(24);
alter table oa_fin_category modify delete_by varchar(24);
alter table oa_fin_category modify tenant_id varchar(24);
alter table oa_fin_collect_ticket modify create_by varchar(24);
alter table oa_fin_collect_ticket modify update_by varchar(24);
alter table oa_fin_collect_ticket modify delete_by varchar(24);
alter table oa_fin_collect_ticket modify tenant_id varchar(24);
alter table oa_fin_collection_no_ticket modify create_by varchar(24);
alter table oa_fin_collection_no_ticket modify update_by varchar(24);
alter table oa_fin_collection_no_ticket modify delete_by varchar(24);
alter table oa_fin_collection_no_ticket modify tenant_id varchar(24);
alter table oa_fin_collection_record modify create_by varchar(24);
alter table oa_fin_collection_record modify update_by varchar(24);
alter table oa_fin_collection_record modify delete_by varchar(24);
alter table oa_fin_collection_record modify tenant_id varchar(24);
alter table oa_fin_company_subject modify create_by varchar(24);
alter table oa_fin_company_subject modify update_by varchar(24);
alter table oa_fin_company_subject modify delete_by varchar(24);
alter table oa_fin_company_subject modify tenant_id varchar(24);
alter table oa_fin_cost_type modify create_by varchar(24);
alter table oa_fin_cost_type modify update_by varchar(24);
alter table oa_fin_cost_type modify delete_by varchar(24);
alter table oa_fin_cost_type modify tenant_id varchar(24);
alter table oa_fin_invoice_information modify create_by varchar(24);
alter table oa_fin_invoice_information modify update_by varchar(24);
alter table oa_fin_invoice_information modify delete_by varchar(24);
alter table oa_fin_invoice_information modify tenant_id varchar(24);
alter table oa_fin_invoicing_apply modify create_by varchar(24);
alter table oa_fin_invoicing_apply modify update_by varchar(24);
alter table oa_fin_invoicing_apply modify delete_by varchar(24);
alter table oa_fin_invoicing_apply modify tenant_id varchar(24);
alter table oa_fin_payment_no_ticket modify create_by varchar(24);
alter table oa_fin_payment_no_ticket modify update_by varchar(24);
alter table oa_fin_payment_no_ticket modify delete_by varchar(24);
alter table oa_fin_payment_no_ticket modify tenant_id varchar(24);
alter table oa_fin_payment_record modify create_by varchar(24);
alter table oa_fin_payment_record modify update_by varchar(24);
alter table oa_fin_payment_record modify delete_by varchar(24);
alter table oa_fin_payment_record modify tenant_id varchar(24);
alter table oa_fin_reimburse_apply modify create_by varchar(24);
alter table oa_fin_reimburse_apply modify update_by varchar(24);
alter table oa_fin_reimburse_apply modify delete_by varchar(24);
alter table oa_fin_reimburse_apply modify tenant_id varchar(24);
alter table oa_fin_reimburse_item modify create_by varchar(24);
alter table oa_fin_reimburse_item modify update_by varchar(24);
alter table oa_fin_reimburse_item modify delete_by varchar(24);
alter table oa_fin_reimburse_item modify tenant_id varchar(24);
alter table oa_fin_reimbursement_type modify create_by varchar(24);
alter table oa_fin_reimbursement_type modify update_by varchar(24);
alter table oa_fin_reimbursement_type modify delete_by varchar(24);
alter table oa_fin_reimbursement_type modify tenant_id varchar(24);
alter table oa_meeting_room modify create_by varchar(24);
alter table oa_meeting_room modify update_by varchar(24);
alter table oa_meeting_room modify delete_by varchar(24);
alter table oa_meeting_room modify tenant_id varchar(24);
alter table oa_meeting_room_book_rcd modify create_by varchar(24);
alter table oa_meeting_room_book_rcd modify update_by varchar(24);
alter table oa_meeting_room_book_rcd modify delete_by varchar(24);
alter table oa_meeting_room_book_rcd modify tenant_id varchar(24);
alter table oa_meeting_room_position modify create_by varchar(24);
alter table oa_meeting_room_position modify update_by varchar(24);
alter table oa_meeting_room_position modify delete_by varchar(24);
alter table oa_meeting_room_position modify tenant_id varchar(24);
alter table oa_netdisk_file modify create_by varchar(24);
alter table oa_netdisk_file modify update_by varchar(24);
alter table oa_netdisk_file modify delete_by varchar(24);
alter table oa_netdisk_folder modify create_by varchar(24);
alter table oa_netdisk_folder modify update_by varchar(24);
alter table oa_netdisk_folder modify delete_by varchar(24);
alter table oa_netdisk_folder modify tenant_id varchar(24);
alter table oa_netdisk_menu modify create_by varchar(24);
alter table oa_netdisk_menu modify update_by varchar(24);
alter table oa_netdisk_menu modify delete_by varchar(24);
alter table oa_netdisk_menu modify tenant_id varchar(24);
alter table oa_netdisk_my_favorite modify create_by varchar(24);
alter table oa_netdisk_my_favorite modify update_by varchar(24);
alter table oa_netdisk_my_favorite modify delete_by varchar(24);
alter table oa_netdisk_my_favorite modify tenant_id varchar(24);
alter table oa_netdisk_my_share modify create_by varchar(24);
alter table oa_netdisk_my_share modify update_by varchar(24);
alter table oa_netdisk_my_share modify delete_by varchar(24);
alter table oa_netdisk_my_share modify tenant_id varchar(24);
alter table oa_netdisk_oper_record modify create_by varchar(24);
alter table oa_netdisk_oper_record modify update_by varchar(24);
alter table oa_netdisk_oper_record modify delete_by varchar(24);
alter table oa_netdisk_oper_record modify tenant_id varchar(24);
alter table oa_netdisk_origin_file modify create_by varchar(24);
alter table oa_netdisk_origin_file modify update_by varchar(24);
alter table oa_netdisk_origin_file modify delete_by varchar(24);
alter table oa_netdisk_origin_file modify tenant_id varchar(24);
alter table oa_netdisk_recycle modify create_by varchar(24);
alter table oa_netdisk_recycle modify update_by varchar(24);
alter table oa_netdisk_recycle modify delete_by varchar(24);
alter table oa_netdisk_recycle modify tenant_id varchar(24);
alter table oa_netdisk_resource_limit modify create_by varchar(24);
alter table oa_netdisk_resource_limit modify update_by varchar(24);
alter table oa_netdisk_resource_limit modify delete_by varchar(24);
alter table oa_netdisk_resource_limit modify tenant_id varchar(24);
alter table oa_netdisk_share_me modify create_by varchar(24);
alter table oa_netdisk_share_me modify update_by varchar(24);
alter table oa_netdisk_share_me modify delete_by varchar(24);
alter table oa_netdisk_share_me modify tenant_id varchar(24);
alter table oa_netdisk_storage modify create_by varchar(24);
alter table oa_netdisk_storage modify update_by varchar(24);
alter table oa_netdisk_storage modify delete_by varchar(24);
alter table oa_netdisk_storage modify tenant_id varchar(24);
alter table oa_netdisk_virtual_fd modify create_by varchar(24);
alter table oa_netdisk_virtual_fd modify update_by varchar(24);
alter table oa_netdisk_virtual_fd modify delete_by varchar(24);
alter table oa_notice modify create_by varchar(24);
alter table oa_notice modify update_by varchar(24);
alter table oa_notice modify delete_by varchar(24);
alter table oa_notice modify tenant_id varchar(24);
alter table oa_notice_priv modify create_by varchar(24);
alter table oa_notice_priv modify update_by varchar(24);
alter table oa_notice_priv modify delete_by varchar(24);
alter table oa_portal modify create_by varchar(24);
alter table oa_portal modify update_by varchar(24);
alter table oa_portal modify delete_by varchar(24);
alter table oa_portal modify tenant_id varchar(24);
alter table oa_project modify create_by varchar(24);
alter table oa_project modify update_by varchar(24);
alter table oa_project modify delete_by varchar(24);
alter table oa_project modify tenant_id varchar(24);
alter table oa_project_doc modify create_by varchar(24);
alter table oa_project_doc modify update_by varchar(24);
alter table oa_project_doc modify delete_by varchar(24);
alter table oa_project_phase modify create_by varchar(24);
alter table oa_project_phase modify update_by varchar(24);
alter table oa_project_phase modify delete_by varchar(24);
alter table oa_project_rcd modify create_by varchar(24);
alter table oa_project_rcd modify update_by varchar(24);
alter table oa_project_rcd modify delete_by varchar(24);
alter table oa_project_task modify create_by varchar(24);
alter table oa_project_task modify update_by varchar(24);
alter table oa_project_task modify delete_by varchar(24);
alter table oa_project_task_rcd modify create_by varchar(24);
alter table oa_project_task_rcd modify update_by varchar(24);
alter table oa_project_task_rcd modify delete_by varchar(24);
alter table oa_project_user modify create_by varchar(24);
alter table oa_project_user modify update_by varchar(24);
alter table oa_project_user modify delete_by varchar(24);
alter table oa_room modify create_by varchar(24);
alter table oa_room modify update_by varchar(24);
alter table oa_room modify delete_by varchar(24);
alter table oa_room modify tenant_id varchar(24);
alter table oa_room_apply modify create_by varchar(24);
alter table oa_room_apply modify update_by varchar(24);
alter table oa_room_apply modify delete_by varchar(24);
alter table oa_room_apply modify tenant_id varchar(24);
alter table oa_room_position modify create_by varchar(24);
alter table oa_room_position modify update_by varchar(24);
alter table oa_room_position modify delete_by varchar(24);
alter table oa_room_position modify tenant_id varchar(24);
alter table oa_room_record modify create_by varchar(24);
alter table oa_room_record modify update_by varchar(24);
alter table oa_room_record modify delete_by varchar(24);
alter table oa_room_record modify tenant_id varchar(24);
alter table oa_schedule_plan modify create_by varchar(24);
alter table oa_schedule_plan modify update_by varchar(24);
alter table oa_schedule_plan modify delete_by varchar(24);
alter table oa_schedule_plan modify tenant_id varchar(24);
alter table oa_seal_apply modify id varchar(24);
alter table oa_seal_apply modify tenant_id varchar(24);
alter table oa_seal_apply modify create_by varchar(24);
alter table oa_seal_apply modify update_by varchar(24);
alter table oa_seal_apply modify delete_by varchar(24);
alter table oa_seal_info modify id varchar(24);
alter table oa_seal_info modify tenant_id varchar(24);
alter table oa_seal_info modify create_by varchar(24);
alter table oa_seal_info modify update_by varchar(24);
alter table oa_seal_info modify delete_by varchar(24);
alter table oa_system_information modify create_by varchar(24);
alter table oa_system_information modify update_by varchar(24);
alter table oa_system_information modify delete_by varchar(24);
alter table oa_system_information modify tenant_id varchar(24);
alter table oa_test modify create_by varchar(24);
alter table oa_test modify update_by varchar(24);
alter table oa_test modify delete_by varchar(24);
alter table oa_test modify tenant_id varchar(24);
alter table oa_vehicle_a_select_item modify create_by varchar(24);
alter table oa_vehicle_a_select_item modify update_by varchar(24);
alter table oa_vehicle_a_select_item modify delete_by varchar(24);
alter table oa_vehicle_apply modify create_by varchar(24);
alter table oa_vehicle_apply modify update_by varchar(24);
alter table oa_vehicle_apply modify delete_by varchar(24);
alter table oa_vehicle_apply modify tenant_id varchar(24);
alter table oa_vehicle_driver_sel modify create_by varchar(24);
alter table oa_vehicle_driver_sel modify update_by varchar(24);
alter table oa_vehicle_driver_sel modify delete_by varchar(24);
alter table oa_vehicle_driver_sel modify tenant_id varchar(24);
alter table oa_vehicle_info modify create_by varchar(24);
alter table oa_vehicle_info modify update_by varchar(24);
alter table oa_vehicle_info modify delete_by varchar(24);
alter table oa_vehicle_info modify tenant_id varchar(24);
alter table oa_vehicle_insurance_com modify create_by varchar(24);
alter table oa_vehicle_insurance_com modify update_by varchar(24);
alter table oa_vehicle_insurance_com modify delete_by varchar(24);
alter table oa_vehicle_insurance_com modify tenant_id varchar(24);
alter table oa_vehicle_insurance_company modify create_by varchar(24);
alter table oa_vehicle_insurance_company modify update_by varchar(24);
alter table oa_vehicle_insurance_company modify delete_by varchar(24);
alter table oa_vehicle_insurance_company modify tenant_id varchar(24);
alter table oa_vehicle_insurance_record modify create_by varchar(24);
alter table oa_vehicle_insurance_record modify update_by varchar(24);
alter table oa_vehicle_insurance_record modify delete_by varchar(24);
alter table oa_vehicle_insurance_record modify tenant_id varchar(24);
alter table oa_vehicle_m_select_item modify create_by varchar(24);
alter table oa_vehicle_m_select_item modify update_by varchar(24);
alter table oa_vehicle_m_select_item modify delete_by varchar(24);
alter table oa_vehicle_maintenance modify create_by varchar(24);
alter table oa_vehicle_maintenance modify update_by varchar(24);
alter table oa_vehicle_maintenance modify delete_by varchar(24);
alter table oa_vehicle_maintenance modify tenant_id varchar(24);
alter table oa_vehicle_position modify create_by varchar(24);
alter table oa_vehicle_position modify update_by varchar(24);
alter table oa_vehicle_position modify delete_by varchar(24);
alter table oa_vehicle_position modify tenant_id varchar(24);
alter table oa_vehicle_record modify create_by varchar(24);
alter table oa_vehicle_record modify update_by varchar(24);
alter table oa_vehicle_record modify delete_by varchar(24);
alter table oa_vehicle_record modify tenant_id varchar(24);
alter table oa_vehicle_select_item modify create_by varchar(24);
alter table oa_vehicle_select_item modify update_by varchar(24);
alter table oa_vehicle_select_item modify delete_by varchar(24);
alter table oa_visitor_record modify create_by varchar(24);
alter table oa_visitor_record modify update_by varchar(24);
alter table oa_visitor_record modify delete_by varchar(24);
alter table oa_visitor_record modify tenant_id varchar(24);
alter table oa_work_plan modify create_by varchar(24);
alter table oa_work_plan modify update_by varchar(24);
alter table oa_work_plan modify delete_by varchar(24);
alter table oa_work_plan modify tenant_id varchar(24);
alter table oa_work_rcd modify create_by varchar(24);
alter table oa_work_rcd modify update_by varchar(24);
alter table oa_work_rcd modify delete_by varchar(24);
alter table oa_work_rcd modify tenant_id varchar(24);
alter table oa_work_rpt modify create_by varchar(24);
alter table oa_work_rpt modify update_by varchar(24);
alter table oa_work_rpt modify delete_by varchar(24);
alter table oa_work_rpt modify tenant_id varchar(24);
alter table ops_auto_action modify create_by varchar(24);
alter table ops_auto_action modify update_by varchar(24);
alter table ops_auto_action modify delete_by varchar(24);
alter table ops_auto_action modify tenant_id varchar(24);
alter table ops_auto_action_file modify update_by varchar(24);
alter table ops_auto_action_file modify delete_by varchar(24);
alter table ops_auto_action_file modify tenant_id varchar(24);
alter table ops_auto_action_file modify create_by varchar(24);
alter table ops_auto_action_s_file modify update_by varchar(24);
alter table ops_auto_action_s_file modify delete_by varchar(24);
alter table ops_auto_action_s_script modify update_by varchar(24);
alter table ops_auto_action_s_script modify delete_by varchar(24);
alter table ops_auto_action_script modify update_by varchar(24);
alter table ops_auto_action_script modify delete_by varchar(24);
alter table ops_auto_action_script modify tenant_id varchar(24);
alter table ops_auto_action_vars modify update_by varchar(24);
alter table ops_auto_action_vars modify delete_by varchar(24);
alter table ops_auto_action_vars modify tenant_id varchar(24);
alter table ops_auto_batch modify update_by varchar(24);
alter table ops_auto_batch modify delete_by varchar(24);
alter table ops_auto_batch modify tenant_id varchar(24);
alter table ops_auto_batch_node modify update_by varchar(24);
alter table ops_auto_batch_node modify delete_by varchar(24);
alter table ops_auto_batch_node modify tenant_id varchar(24);
alter table ops_auto_group modify update_by varchar(24);
alter table ops_auto_group modify delete_by varchar(24);
alter table ops_auto_group modify tenant_id varchar(24);
alter table ops_auto_group modify create_by varchar(24);
alter table ops_auto_node modify update_by varchar(24);
alter table ops_auto_node modify delete_by varchar(24);
alter table ops_auto_node modify tenant_id varchar(24);
alter table ops_auto_node modify create_by varchar(24);
alter table ops_auto_node_select modify update_by varchar(24);
alter table ops_auto_node_select modify delete_by varchar(24);
alter table ops_auto_node_select modify tenant_id varchar(24);
alter table ops_auto_task modify update_by varchar(24);
alter table ops_auto_task modify delete_by varchar(24);
alter table ops_auto_task modify tenant_id varchar(24);
alter table ops_auto_task modify create_by varchar(24);
alter table ops_auto_task_log modify update_by varchar(24);
alter table ops_auto_task_log modify delete_by varchar(24);
alter table ops_auto_task_log modify create_by varchar(24);
alter table ops_auto_task_log_dtl modify update_by varchar(24);
alter table ops_auto_task_log_dtl modify delete_by varchar(24);
alter table ops_auto_task_log_dtl modify create_by varchar(24);
alter table ops_auto_task_m_log modify update_by varchar(24);
alter table ops_auto_task_m_log modify delete_by varchar(24);
alter table ops_auto_task_m_log modify create_by varchar(24);
alter table ops_auto_task_node modify update_by varchar(24);
alter table ops_auto_task_node modify delete_by varchar(24);
alter table ops_auto_voucher modify create_by varchar(24);
alter table ops_auto_voucher modify update_by varchar(24);
alter table ops_auto_voucher modify delete_by varchar(24);
alter table ops_automatic_conf modify create_by varchar(24);
alter table ops_automatic_conf modify update_by varchar(24);
alter table ops_automatic_conf modify delete_by varchar(24);
alter table ops_automatic_file modify create_by varchar(24);
alter table ops_automatic_file modify update_by varchar(24);
alter table ops_automatic_file modify delete_by varchar(24);
alter table ops_automatic_instance modify create_by varchar(24);
alter table ops_automatic_instance modify update_by varchar(24);
alter table ops_automatic_instance modify delete_by varchar(24);
alter table ops_automatic_oper_log modify create_by varchar(24);
alter table ops_automatic_oper_log modify update_by varchar(24);
alter table ops_automatic_oper_log modify delete_by varchar(24);
alter table ops_automatic_tpl modify create_by varchar(24);
alter table ops_automatic_tpl modify update_by varchar(24);
alter table ops_automatic_tpl modify delete_by varchar(24);
alter table ops_certificate modify update_by varchar(24);
alter table ops_certificate modify delete_by varchar(24);
alter table ops_certificate modify tenant_id varchar(24);
alter table ops_certificate_item modify update_by varchar(24);
alter table ops_certificate_item modify delete_by varchar(24);
alter table ops_certificate_item modify tenant_id varchar(24);
alter table ops_certificate_position modify update_by varchar(24);
alter table ops_certificate_position modify delete_by varchar(24);
alter table ops_certificate_position modify tenant_id varchar(24);
alter table ops_certificate_type modify code varchar(24);
alter table ops_certificate_type modify update_by varchar(24);
alter table ops_certificate_type modify delete_by varchar(24);
alter table ops_certificate_type modify tenant_id varchar(24);
alter table ops_ciphertext_box modify update_by varchar(24);
alter table ops_ciphertext_box modify delete_by varchar(24);
alter table ops_ciphertext_box_data modify create_by varchar(24);
alter table ops_ciphertext_box_data modify update_by varchar(24);
alter table ops_ciphertext_box_data modify delete_by varchar(24);
alter table ops_ciphertext_conf modify update_by varchar(24);
alter table ops_ciphertext_conf modify delete_by varchar(24);
alter table ops_ciphertext_history modify update_by varchar(24);
alter table ops_ciphertext_history modify delete_by varchar(24);
alter table ops_cmdb_attribute_c modify create_by varchar(24);
alter table ops_cmdb_attribute_c modify update_by varchar(24);
alter table ops_cmdb_attribute_c modify delete_by varchar(24);
alter table ops_cmdb_attribute_c modify tenant_id varchar(24);
alter table ops_cmdb_log modify create_by varchar(24);
alter table ops_cmdb_log modify update_by varchar(24);
alter table ops_cmdb_log modify delete_by varchar(24);
alter table ops_cmdb_log modify tenant_id varchar(24);
alter table ops_cmdb_model modify create_by varchar(24);
alter table ops_cmdb_model modify update_by varchar(24);
alter table ops_cmdb_model modify delete_by varchar(24);
alter table ops_cmdb_model modify tenant_id varchar(24);
alter table ops_cmdb_model_v modify create_by varchar(24);
alter table ops_cmdb_model_v modify update_by varchar(24);
alter table ops_cmdb_model_v modify delete_by varchar(24);
alter table ops_cmdb_model_v modify tenant_id varchar(24);
alter table ops_cmdb_model_v_h modify create_by varchar(24);
alter table ops_cmdb_model_v_h modify update_by varchar(24);
alter table ops_cmdb_model_v_h modify delete_by varchar(24);
alter table ops_cmdb_model_v_h modify tenant_id varchar(24);
alter table ops_cmdb_obj_attribute modify create_by varchar(24);
alter table ops_cmdb_obj_attribute modify update_by varchar(24);
alter table ops_cmdb_obj_attribute modify delete_by varchar(24);
alter table ops_cmdb_obj_attribute modify tenant_id varchar(24);
alter table ops_db_apply_execute modify update_by varchar(24);
alter table ops_db_apply_execute modify delete_by varchar(24);
alter table ops_db_apply_execute modify create_by varchar(24);
alter table ops_db_apply_file modify update_by varchar(24);
alter table ops_db_apply_file modify delete_by varchar(24);
alter table ops_db_apply_file modify create_by varchar(24);
alter table ops_db_backup_info modify create_by varchar(24);
alter table ops_db_backup_info modify update_by varchar(24);
alter table ops_db_backup_info modify delete_by varchar(24);
alter table ops_db_backup_log modify create_by varchar(24);
alter table ops_db_backup_log modify update_by varchar(24);
alter table ops_db_backup_log modify delete_by varchar(24);
alter table ops_db_backup_record modify create_by varchar(24);
alter table ops_db_backup_record modify update_by varchar(24);
alter table ops_db_backup_record modify delete_by varchar(24);
alter table ops_db_capacity_rpt modify create_by varchar(24);
alter table ops_db_capacity_rpt modify update_by varchar(24);
alter table ops_db_capacity_rpt modify delete_by varchar(24);
alter table ops_db_capacity_rpt modify tenant_id varchar(24);
alter table ops_db_change_apply modify update_by varchar(24);
alter table ops_db_change_apply modify delete_by varchar(24);
alter table ops_db_change_apply modify create_by varchar(24);
alter table ops_db_change_apply modify tenant_id varchar(24);
alter table ops_db_data_loc modify create_by varchar(24);
alter table ops_db_data_loc modify update_by varchar(24);
alter table ops_db_data_loc modify delete_by varchar(24);
alter table ops_db_doc modify create_by varchar(24);
alter table ops_db_doc modify update_by varchar(24);
alter table ops_db_doc modify delete_by varchar(24);
alter table ops_db_env_info modify create_by varchar(24);
alter table ops_db_env_info modify update_by varchar(24);
alter table ops_db_env_info modify delete_by varchar(24);
alter table ops_db_execute_user modify update_by varchar(24);
alter table ops_db_execute_user modify delete_by varchar(24);
alter table ops_db_execute_user modify create_by varchar(24);
alter table ops_db_extract_apply modify update_by varchar(24);
alter table ops_db_extract_apply modify delete_by varchar(24);
alter table ops_db_extract_apply modify create_by varchar(24);
alter table ops_db_extract_apply modify tenant_id varchar(24);
alter table ops_db_info modify create_by varchar(24);
alter table ops_db_info modify update_by varchar(24);
alter table ops_db_info modify delete_by varchar(24);
alter table ops_db_info_apply modify update_by varchar(24);
alter table ops_db_info_apply modify delete_by varchar(24);
alter table ops_db_info_apply modify create_by varchar(24);
alter table ops_db_info_label modify create_by varchar(24);
alter table ops_db_info_label modify update_by varchar(24);
alter table ops_db_info_label modify delete_by varchar(24);
alter table ops_db_inspection modify create_by varchar(24);
alter table ops_db_inspection modify update_by varchar(24);
alter table ops_db_inspection modify delete_by varchar(24);
alter table ops_db_inspection modify tenant_id varchar(24);
alter table ops_db_instance modify create_by varchar(24);
alter table ops_db_instance modify update_by varchar(24);
alter table ops_db_instance modify delete_by varchar(24);
alter table ops_db_instance modify tenant_id varchar(24);
alter table ops_db_report modify create_by varchar(24);
alter table ops_db_report modify update_by varchar(24);
alter table ops_db_report modify delete_by varchar(24);
alter table ops_deploy_standard modify create_by varchar(24);
alter table ops_deploy_standard modify update_by varchar(24);
alter table ops_deploy_standard modify delete_by varchar(24);
alter table ops_deploy_standard modify tenant_id varchar(24);
alter table ops_host modify create_by varchar(24);
alter table ops_host modify update_by varchar(24);
alter table ops_host modify delete_by varchar(24);
alter table ops_host modify tenant_id varchar(24);
alter table ops_host_db modify create_by varchar(24);
alter table ops_host_db modify update_by varchar(24);
alter table ops_host_db modify delete_by varchar(24);
alter table ops_host_env_info modify create_by varchar(24);
alter table ops_host_env_info modify update_by varchar(24);
alter table ops_host_env_info modify delete_by varchar(24);
alter table ops_host_ex_by_db modify create_by varchar(24);
alter table ops_host_ex_by_db modify update_by varchar(24);
alter table ops_host_ex_by_db modify delete_by varchar(24);
alter table ops_host_ex_by_db modify tenant_id varchar(24);
alter table ops_host_ex_by_host modify create_by varchar(24);
alter table ops_host_ex_by_host modify update_by varchar(24);
alter table ops_host_ex_by_host modify delete_by varchar(24);
alter table ops_host_ex_by_host modify tenant_id varchar(24);
alter table ops_host_mid modify create_by varchar(24);
alter table ops_host_mid modify update_by varchar(24);
alter table ops_host_mid modify delete_by varchar(24);
alter table ops_host_os modify create_by varchar(24);
alter table ops_host_os modify update_by varchar(24);
alter table ops_host_os modify delete_by varchar(24);
alter table ops_host_position modify create_by varchar(24);
alter table ops_host_position modify update_by varchar(24);
alter table ops_host_position modify delete_by varchar(24);
alter table ops_host_position modify tenant_id varchar(24);
alter table ops_host_service modify create_by varchar(24);
alter table ops_host_service modify update_by varchar(24);
alter table ops_host_service modify delete_by varchar(24);
alter table ops_host_service modify tenant_id varchar(24);
alter table ops_information_system modify create_by varchar(24);
alter table ops_information_system modify update_by varchar(24);
alter table ops_information_system modify delete_by varchar(24);
alter table ops_information_system modify tenant_id varchar(24);
alter table ops_ip_addr modify create_by varchar(24);
alter table ops_ip_addr modify update_by varchar(24);
alter table ops_ip_addr modify delete_by varchar(24);
alter table ops_ip_addr modify tenant_id varchar(24);
alter table ops_ip_address_range modify create_by varchar(24);
alter table ops_ip_address_range modify update_by varchar(24);
alter table ops_ip_address_range modify delete_by varchar(24);
alter table ops_ip_address_range modify tenant_id varchar(24);
alter table ops_ip_group modify create_by varchar(24);
alter table ops_ip_group modify update_by varchar(24);
alter table ops_ip_group modify delete_by varchar(24);
alter table ops_ip_group modify tenant_id varchar(24);
alter table ops_monitor_alert modify create_by varchar(24);
alter table ops_monitor_alert modify update_by varchar(24);
alter table ops_monitor_alert modify delete_by varchar(24);
alter table ops_monitor_alert_book modify create_by varchar(24);
alter table ops_monitor_alert_book modify update_by varchar(24);
alter table ops_monitor_alert_book modify delete_by varchar(24);
alter table ops_monitor_alert_book_rule modify create_by varchar(24);
alter table ops_monitor_alert_book_rule modify update_by varchar(24);
alter table ops_monitor_alert_book_rule modify delete_by varchar(24);
alter table ops_monitor_alert_event modify create_by varchar(24);
alter table ops_monitor_alert_event modify update_by varchar(24);
alter table ops_monitor_alert_event modify delete_by varchar(24);
alter table ops_monitor_alert_log modify create_by varchar(24);
alter table ops_monitor_alert_log modify update_by varchar(24);
alter table ops_monitor_alert_log modify delete_by varchar(24);
alter table ops_monitor_alert_method modify create_by varchar(24);
alter table ops_monitor_alert_method modify update_by varchar(24);
alter table ops_monitor_alert_method modify delete_by varchar(24);
alter table ops_monitor_node modify create_by varchar(24);
alter table ops_monitor_node modify update_by varchar(24);
alter table ops_monitor_node modify delete_by varchar(24);
alter table ops_monitor_node_db modify create_by varchar(24);
alter table ops_monitor_node_db modify update_by varchar(24);
alter table ops_monitor_node_db modify delete_by varchar(24);
alter table ops_monitor_node_group modify create_by varchar(24);
alter table ops_monitor_node_group modify update_by varchar(24);
alter table ops_monitor_node_group modify delete_by varchar(24);
alter table ops_monitor_node_host modify create_by varchar(24);
alter table ops_monitor_node_host modify update_by varchar(24);
alter table ops_monitor_node_host modify delete_by varchar(24);
alter table ops_monitor_node_list_value modify create_by varchar(24);
alter table ops_monitor_node_list_value modify update_by varchar(24);
alter table ops_monitor_node_list_value modify delete_by varchar(24);
alter table ops_monitor_node_map modify create_by varchar(24);
alter table ops_monitor_node_map modify update_by varchar(24);
alter table ops_monitor_node_map modify delete_by varchar(24);
alter table ops_monitor_node_subtype modify create_by varchar(24);
alter table ops_monitor_node_subtype modify update_by varchar(24);
alter table ops_monitor_node_subtype modify delete_by varchar(24);
alter table ops_monitor_node_tpl_item modify create_by varchar(24);
alter table ops_monitor_node_tpl_item modify update_by varchar(24);
alter table ops_monitor_node_tpl_item modify delete_by varchar(24);
alter table ops_monitor_node_trigger modify create_by varchar(24);
alter table ops_monitor_node_trigger modify update_by varchar(24);
alter table ops_monitor_node_trigger modify delete_by varchar(24);
alter table ops_monitor_node_type modify create_by varchar(24);
alter table ops_monitor_node_type modify update_by varchar(24);
alter table ops_monitor_node_type modify delete_by varchar(24);
alter table ops_monitor_node_value modify create_by varchar(24);
alter table ops_monitor_node_value modify update_by varchar(24);
alter table ops_monitor_node_value modify delete_by varchar(24);
alter table ops_monitor_node_value_last modify create_by varchar(24);
alter table ops_monitor_node_value_last modify update_by varchar(24);
alter table ops_monitor_node_value_last modify delete_by varchar(24);
alter table ops_monitor_object modify create_by varchar(24);
alter table ops_monitor_object modify update_by varchar(24);
alter table ops_monitor_object modify delete_by varchar(24);
alter table ops_monitor_object_grafana modify create_by varchar(24);
alter table ops_monitor_object_grafana modify update_by varchar(24);
alter table ops_monitor_object_grafana modify delete_by varchar(24);
alter table ops_monitor_object_group modify create_by varchar(24);
alter table ops_monitor_object_group modify update_by varchar(24);
alter table ops_monitor_object_group modify delete_by varchar(24);
alter table ops_monitor_object_map modify create_by varchar(24);
alter table ops_monitor_object_map modify update_by varchar(24);
alter table ops_monitor_object_map modify delete_by varchar(24);
alter table ops_monitor_object_model modify create_by varchar(24);
alter table ops_monitor_object_model modify update_by varchar(24);
alter table ops_monitor_object_model modify delete_by varchar(24);
alter table ops_monitor_object_tpl modify create_by varchar(24);
alter table ops_monitor_object_tpl modify update_by varchar(24);
alter table ops_monitor_object_tpl modify delete_by varchar(24);
alter table ops_monitor_object_tree modify create_by varchar(24);
alter table ops_monitor_object_tree modify update_by varchar(24);
alter table ops_monitor_object_tree modify delete_by varchar(24);
alter table ops_monitor_tpl modify create_by varchar(24);
alter table ops_monitor_tpl modify update_by varchar(24);
alter table ops_monitor_tpl modify delete_by varchar(24);
alter table ops_monitor_tpl_graph modify create_by varchar(24);
alter table ops_monitor_tpl_graph modify update_by varchar(24);
alter table ops_monitor_tpl_graph modify delete_by varchar(24);
alter table ops_monitor_tpl_graph_item modify create_by varchar(24);
alter table ops_monitor_tpl_graph_item modify update_by varchar(24);
alter table ops_monitor_tpl_graph_item modify delete_by varchar(24);
alter table ops_monitor_tpl_indicator modify create_by varchar(24);
alter table ops_monitor_tpl_indicator modify update_by varchar(24);
alter table ops_monitor_tpl_indicator modify delete_by varchar(24);
alter table ops_monitor_tpl_indicator_type modify create_by varchar(24);
alter table ops_monitor_tpl_indicator_type modify update_by varchar(24);
alter table ops_monitor_tpl_indicator_type modify delete_by varchar(24);
alter table ops_monitor_tpl_trigger modify create_by varchar(24);
alter table ops_monitor_tpl_trigger modify update_by varchar(24);
alter table ops_monitor_tpl_trigger modify delete_by varchar(24);
alter table ops_monitor_tpl_type modify create_by varchar(24);
alter table ops_monitor_tpl_type modify update_by varchar(24);
alter table ops_monitor_tpl_type modify delete_by varchar(24);
alter table ops_monitor_voucher modify create_by varchar(24);
alter table ops_monitor_voucher modify update_by varchar(24);
alter table ops_monitor_voucher modify delete_by varchar(24);
alter table ops_monitor_warn modify create_by varchar(24);
alter table ops_monitor_warn modify update_by varchar(24);
alter table ops_monitor_warn modify delete_by varchar(24);
alter table ops_network_nat_map modify create_by varchar(24);
alter table ops_network_nat_map modify update_by varchar(24);
alter table ops_network_nat_map modify delete_by varchar(24);
alter table ops_network_nat_map modify tenant_id varchar(24);
alter table ops_ops_host modify create_by varchar(24);
alter table ops_ops_host modify update_by varchar(24);
alter table ops_ops_host modify delete_by varchar(24);
alter table ops_ops_host modify tenant_id varchar(24);
alter table ops_ops_host_group modify create_by varchar(24);
alter table ops_ops_host_group modify update_by varchar(24);
alter table ops_ops_host_group modify delete_by varchar(24);
alter table ops_ops_host_group modify tenant_id varchar(24);
alter table ops_ops_host_group_item modify create_by varchar(24);
alter table ops_ops_host_group_item modify update_by varchar(24);
alter table ops_ops_host_group_item modify delete_by varchar(24);
alter table ops_ops_host_group_item modify tenant_id varchar(24);
alter table ops_ops_host_voucher modify create_by varchar(24);
alter table ops_ops_host_voucher modify update_by varchar(24);
alter table ops_ops_host_voucher modify delete_by varchar(24);
alter table ops_ops_host_voucher modify tenant_id varchar(24);
alter table ops_person modify create_by varchar(24);
alter table ops_person modify update_by varchar(24);
alter table ops_person modify delete_by varchar(24);
alter table ops_person modify tenant_id varchar(24);
alter table ops_personnel_division modify create_by varchar(24);
alter table ops_personnel_division modify update_by varchar(24);
alter table ops_personnel_division modify delete_by varchar(24);
alter table ops_public_content modify create_by varchar(24);
alter table ops_public_content modify update_by varchar(24);
alter table ops_public_content modify delete_by varchar(24);
alter table ops_safety_baseline modify create_by varchar(24);
alter table ops_safety_baseline modify update_by varchar(24);
alter table ops_safety_baseline modify delete_by varchar(24);
alter table ops_safety_baseline modify tenant_id varchar(24);
alter table ops_security_info_lib modify create_by varchar(24);
alter table ops_security_info_lib modify update_by varchar(24);
alter table ops_security_info_lib modify delete_by varchar(24);
alter table ops_security_info_lib modify tenant_id varchar(24);
alter table ops_service_category modify create_by varchar(24);
alter table ops_service_category modify update_by varchar(24);
alter table ops_service_category modify delete_by varchar(24);
alter table ops_service_category modify tenant_id varchar(24);
alter table ops_service_category_label modify create_by varchar(24);
alter table ops_service_category_label modify update_by varchar(24);
alter table ops_service_category_label modify delete_by varchar(24);
alter table ops_service_category_label modify tenant_id varchar(24);
alter table ops_service_group modify create_by varchar(24);
alter table ops_service_group modify update_by varchar(24);
alter table ops_service_group modify delete_by varchar(24);
alter table ops_service_group modify tenant_id varchar(24);
alter table ops_service_info modify create_by varchar(24);
alter table ops_service_info modify update_by varchar(24);
alter table ops_service_info modify delete_by varchar(24);
alter table ops_service_info modify tenant_id varchar(24);
alter table ops_software_base_type modify create_by varchar(24);
alter table ops_software_base_type modify update_by varchar(24);
alter table ops_software_base_type modify delete_by varchar(24);
alter table ops_software_base_type modify tenant_id varchar(24);
alter table ops_software_base_version modify create_by varchar(24);
alter table ops_software_base_version modify update_by varchar(24);
alter table ops_software_base_version modify delete_by varchar(24);
alter table ops_software_base_version modify tenant_id varchar(24);
alter table ops_software_media modify create_by varchar(24);
alter table ops_software_media modify update_by varchar(24);
alter table ops_software_media modify delete_by varchar(24);
alter table ops_system_console_info modify create_by varchar(24);
alter table ops_system_console_info modify update_by varchar(24);
alter table ops_system_console_info modify delete_by varchar(24);
alter table ops_system_console_info modify tenant_id varchar(24);
alter table ops_voucher modify create_by varchar(24);
alter table ops_voucher modify update_by varchar(24);
alter table ops_voucher modify delete_by varchar(24);
alter table ops_voucher modify tenant_id varchar(24);
alter table ops_voucher_history modify create_by varchar(24);
alter table ops_voucher_history modify update_by varchar(24);
alter table ops_voucher_history modify delete_by varchar(24);
alter table ops_voucher_history modify tenant_id varchar(24);
alter table ops_voucher_owner modify create_by varchar(24);
alter table ops_voucher_owner modify update_by varchar(24);
alter table ops_voucher_owner modify delete_by varchar(24);
alter table ops_voucher_owner modify tenant_id varchar(24);
alter table ops_voucher_priv modify create_by varchar(24);
alter table ops_voucher_priv modify update_by varchar(24);
alter table ops_voucher_priv modify delete_by varchar(24);
alter table ops_voucher_priv modify tenant_id varchar(24);
alter table pcm_catalog modify id varchar(24);
alter table pcm_catalog modify parent_id varchar(24);
alter table pcm_catalog modify tenant_id varchar(24);
alter table pcm_catalog modify create_by varchar(24);
alter table pcm_catalog modify update_by varchar(24);
alter table pcm_catalog modify delete_by varchar(24);
alter table pcm_catalog_allocation modify id varchar(24);
alter table pcm_catalog_allocation modify catalog_id varchar(24);
alter table pcm_catalog_allocation modify attribute_id varchar(24);
alter table pcm_catalog_allocation modify create_by varchar(24);
alter table pcm_catalog_allocation modify update_by varchar(24);
alter table pcm_catalog_allocation modify delete_by varchar(24);
alter table pcm_catalog_attribute modify id varchar(24);
alter table pcm_catalog_attribute modify catalog_id varchar(24);
alter table pcm_catalog_attribute modify source_id varchar(24);
alter table pcm_catalog_attribute modify create_by varchar(24);
alter table pcm_catalog_attribute modify update_by varchar(24);
alter table pcm_catalog_attribute modify delete_by varchar(24);
alter table pcm_data_01 modify id varchar(24);
alter table pcm_data_01 modify catalog_id varchar(24);
alter table pcm_data_01 modify tenant_id varchar(24);
alter table pcm_data_01 modify create_by varchar(24);
alter table pcm_data_01 modify update_by varchar(24);
alter table pcm_data_01 modify delete_by varchar(24);
alter table pcm_data_02 modify id varchar(24);
alter table pcm_data_02 modify catalog_id varchar(24);
alter table pcm_data_02 modify tenant_id varchar(24);
alter table pcm_data_02 modify create_by varchar(24);
alter table pcm_data_02 modify update_by varchar(24);
alter table pcm_data_02 modify delete_by varchar(24);
alter table pcm_data_asset_01 modify id varchar(24);
alter table pcm_data_asset_01 modify tenant_id varchar(24);
alter table pcm_data_asset_01 modify create_by varchar(24);
alter table pcm_data_asset_01 modify update_by varchar(24);
alter table pcm_data_asset_01 modify delete_by varchar(24);
alter table pcm_data_asset_01 modify catalog_id varchar(24);
alter table pcm_data_emp_ext modify id varchar(24);
alter table pcm_data_emp_ext modify catalog_id varchar(24);
alter table pcm_data_emp_ext modify tenant_id varchar(24);
alter table pcm_data_emp_ext modify create_by varchar(24);
alter table pcm_data_emp_ext modify update_by varchar(24);
alter table pcm_data_emp_ext modify delete_by varchar(24);
alter table sys_api_source modify id varchar(24);
alter table sys_api_source modify create_by varchar(24);
alter table sys_api_source modify update_by varchar(24);
alter table sys_api_source modify delete_by varchar(24);
alter table sys_auto_module_role modify id varchar(24);
alter table sys_auto_module_role modify update_by varchar(24);
alter table sys_auto_module_role modify delete_by varchar(24);
alter table sys_auto_module_role_item modify id varchar(24);
alter table sys_auto_module_role_item modify update_by varchar(24);
alter table sys_auto_module_role_item modify delete_by varchar(24);
alter table sys_auto_role_grant_rcd modify id varchar(24);
alter table sys_auto_role_grant_rcd modify create_by varchar(24);
alter table sys_auto_role_grant_rcd modify update_by varchar(24);
alter table sys_auto_role_grant_rcd modify delete_by varchar(24);
alter table sys_auto_user_predefined modify id varchar(24);
alter table sys_auto_user_predefined modify create_by varchar(24);
alter table sys_auto_user_predefined modify update_by varchar(24);
alter table sys_auto_user_predefined modify delete_by varchar(24);
alter table sys_backup_db modify create_by varchar(24);
alter table sys_backup_db modify update_by varchar(24);
alter table sys_backup_db modify delete_by varchar(24);
alter table sys_backup_db_strategy modify create_by varchar(24);
alter table sys_backup_db_strategy modify update_by varchar(24);
alter table sys_backup_db_strategy modify delete_by varchar(24);
alter table sys_bpm_form_data modify create_by varchar(24);
alter table sys_bpm_form_data modify update_by varchar(24);
alter table sys_bpm_form_data modify delete_by varchar(24);
alter table sys_busi_role modify id varchar(24);
alter table sys_busi_role modify create_by varchar(24);
alter table sys_busi_role modify update_by varchar(24);
alter table sys_busi_role modify delete_by varchar(24);
alter table sys_busi_role_member modify id varchar(24);
alter table sys_busi_role_member modify role_id varchar(24);
alter table sys_busi_role_member modify member_id varchar(24);
alter table sys_busi_role_member modify create_by varchar(24);
alter table sys_category modify create_by varchar(24);
alter table sys_category modify update_by varchar(24);
alter table sys_category modify delete_by varchar(24);
alter table sys_category modify tenant_id varchar(24);
alter table sys_code_allocation modify create_by varchar(24);
alter table sys_code_allocation modify update_by varchar(24);
alter table sys_code_allocation modify delete_by varchar(24);
alter table sys_code_attr modify create_by varchar(24);
alter table sys_code_attr modify update_by varchar(24);
alter table sys_code_attr modify delete_by varchar(24);
alter table sys_code_example modify id varchar(24);
alter table sys_code_example modify image_id varchar(24);
alter table sys_code_example modify radio_enum varchar(24);
alter table sys_code_example modify radio_dict varchar(24);
alter table sys_code_example modify create_by varchar(24);
alter table sys_code_example modify update_by varchar(24);
alter table sys_code_example modify delete_by varchar(24);
alter table sys_code_example_car modify id varchar(24);
alter table sys_code_example_car modify create_by varchar(24);
alter table sys_code_example_car modify update_by varchar(24);
alter table sys_code_example_car modify delete_by varchar(24);
alter table sys_code_example_car modify position_id varchar(24);
alter table sys_code_example_car modify org_id varchar(24);
alter table sys_code_example_car modify emp_id varchar(24);
alter table sys_code_example_car modify select_emp_id varchar(24);
alter table sys_code_example_car modify com_id varchar(24);
alter table sys_code_example_car modify sub_org_id varchar(24);
alter table sys_code_example_role modify id varchar(24);
alter table sys_code_example_student modify id varchar(24);
alter table sys_code_example_student modify create_by varchar(24);
alter table sys_code_example_student modify update_by varchar(24);
alter table sys_code_example_student modify delete_by varchar(24);
alter table sys_code_register modify create_by varchar(24);
alter table sys_code_register modify update_by varchar(24);
alter table sys_code_register modify delete_by varchar(24);
alter table sys_code_register modify tenant_id varchar(24);
alter table sys_code_rule modify create_by varchar(24);
alter table sys_code_rule modify update_by varchar(24);
alter table sys_code_rule modify delete_by varchar(24);
alter table sys_config modify catalog_code varchar(24);
alter table sys_config modify create_by varchar(24);
alter table sys_config modify update_by varchar(24);
alter table sys_config modify delete_by varchar(24);
alter table sys_dashboard modify create_by varchar(24);
alter table sys_dashboard modify update_by varchar(24);
alter table sys_dashboard modify delete_by varchar(24);
alter table sys_dashboard modify tenant_id varchar(24);
alter table sys_dashboard_layer modify create_by varchar(24);
alter table sys_dashboard_layer modify update_by varchar(24);
alter table sys_dashboard_layer modify delete_by varchar(24);
alter table sys_dashboard_layer_ele modify create_by varchar(24);
alter table sys_dashboard_layer_ele modify update_by varchar(24);
alter table sys_dashboard_layer_ele modify delete_by varchar(24);
alter table sys_db_cache modify owner_type varchar(24);
alter table sys_db_cache modify create_by varchar(24);
alter table sys_db_cache modify update_by varchar(24);
alter table sys_db_cache modify delete_by varchar(24);
alter table sys_db_sql_change modify create_by varchar(24);
alter table sys_db_sql_change modify update_by varchar(24);
alter table sys_db_sql_change modify delete_by varchar(24);
alter table sys_demo_test modify create_by varchar(24);
alter table sys_demo_test modify update_by varchar(24);
alter table sys_demo_test modify delete_by varchar(24);
alter table sys_demo_test modify tenant_id varchar(24);
alter table sys_dict modify create_by varchar(24);
alter table sys_dict modify update_by varchar(24);
alter table sys_dict modify delete_by varchar(24);
alter table sys_dict_item modify create_by varchar(24);
alter table sys_dict_item modify update_by varchar(24);
alter table sys_dict_item modify delete_by varchar(24);
alter table sys_discrete_info modify create_by varchar(24);
alter table sys_discrete_info modify update_by varchar(24);
alter table sys_discrete_info modify delete_by varchar(24);
alter table sys_discrete_value modify create_by varchar(24);
alter table sys_discrete_value modify update_by varchar(24);
alter table sys_discrete_value modify delete_by varchar(24);
alter table sys_docs_db_update modify create_by varchar(24);
alter table sys_docs_db_update modify update_by varchar(24);
alter table sys_docs_db_update modify delete_by varchar(24);
alter table sys_file modify id varchar(24);
alter table sys_file modify create_by varchar(24);
alter table sys_file modify update_by varchar(24);
alter table sys_file modify delete_by varchar(24);
alter table sys_form_category modify create_by varchar(24);
alter table sys_form_category modify update_by varchar(24);
alter table sys_form_category modify delete_by varchar(24);
alter table sys_form_category modify tenant_id varchar(24);
alter table sys_form_col_map modify create_by varchar(24);
alter table sys_form_col_map modify update_by varchar(24);
alter table sys_form_col_map modify delete_by varchar(24);
alter table sys_form_data modify create_by varchar(24);
alter table sys_form_data modify update_by varchar(24);
alter table sys_form_data modify delete_by varchar(24);
alter table sys_form_data_col modify create_by varchar(24);
alter table sys_form_data_col modify update_by varchar(24);
alter table sys_form_data_col modify delete_by varchar(24);
alter table sys_form_data_ext modify create_by varchar(24);
alter table sys_form_data_ext modify update_by varchar(24);
alter table sys_form_data_ext modify delete_by varchar(24);
alter table sys_form_def modify create_by varchar(24);
alter table sys_form_def modify update_by varchar(24);
alter table sys_form_def modify delete_by varchar(24);
alter table sys_form_info modify create_by varchar(24);
alter table sys_form_info modify update_by varchar(24);
alter table sys_form_info modify delete_by varchar(24);
alter table sys_form_info modify tenant_id varchar(24);
alter table sys_invoke_log modify user_id varchar(24);
alter table sys_invoke_log modify tid varchar(24);
alter table sys_job modify id varchar(24);
alter table sys_job modify worker_id varchar(24);
alter table sys_job modify tenant_id varchar(24);
alter table sys_job modify create_by varchar(24);
alter table sys_job modify update_by varchar(24);
alter table sys_job modify delete_by varchar(24);
alter table sys_job_log modify id varchar(24);
alter table sys_job_log modify user_id varchar(24);
alter table sys_job_log modify node_id varchar(24);
alter table sys_job_worker modify id varchar(24);
alter table sys_key_code modify create_by varchar(24);
alter table sys_key_code modify update_by varchar(24);
alter table sys_key_code modify delete_by varchar(24);
alter table sys_lang modify create_by varchar(24);
alter table sys_lang modify update_by varchar(24);
alter table sys_lang modify delete_by varchar(24);
alter table sys_lang_bak modify create_by varchar(24);
alter table sys_lang_bak modify update_by varchar(24);
alter table sys_lang_bak modify delete_by varchar(24);
alter table sys_lang_lit modify create_by varchar(24);
alter table sys_lang_lit modify update_by varchar(24);
alter table sys_lang_lit modify delete_by varchar(24);
alter table sys_ldap_info modify create_by varchar(24);
alter table sys_ldap_info modify update_by varchar(24);
alter table sys_ldap_info modify delete_by varchar(24);
alter table sys_ldap_info modify tenant_id varchar(24);
alter table sys_licence_switch modify create_by varchar(24);
alter table sys_licence_switch modify update_by varchar(24);
alter table sys_licence_switch modify delete_by varchar(24);
alter table sys_liteflow_chain modify create_by varchar(24);
alter table sys_liteflow_chain modify update_by varchar(24);
alter table sys_liteflow_chain modify delete_by varchar(24);
alter table sys_liteflow_chain modify tenant_id varchar(24);
alter table sys_liteflow_script modify create_by varchar(24);
alter table sys_liteflow_script modify update_by varchar(24);
alter table sys_liteflow_script modify delete_by varchar(24);
alter table sys_liteflow_script modify tenant_id varchar(24);
alter table sys_log_collect modify create_by varchar(24);
alter table sys_log_collect modify update_by varchar(24);
alter table sys_log_collect modify delete_by varchar(24);
alter table sys_mapping_owner modify create_by varchar(24);
alter table sys_mapping_owner modify update_by varchar(24);
alter table sys_mapping_owner modify delete_by varchar(24);
alter table sys_material_resource modify create_by varchar(24);
alter table sys_material_resource modify update_by varchar(24);
alter table sys_material_resource modify delete_by varchar(24);
alter table sys_material_resource modify tenant_id varchar(24);
alter table sys_menu modify id varchar(24);
alter table sys_menu modify batch_id varchar(24);
alter table sys_menu modify path_resource_id varchar(24);
alter table sys_menu modify parent_id varchar(24);
alter table sys_menu modify create_by varchar(24);
alter table sys_menu modify update_by varchar(24);
alter table sys_menu modify delete_by varchar(24);
alter table sys_menu_fork modify id varchar(24);
alter table sys_menu_fork modify menu_id varchar(24);
alter table sys_menu_fork modify create_by varchar(24);
alter table sys_menu_fork modify update_by varchar(24);
alter table sys_menu_fork modify delete_by varchar(24);
alter table sys_menu_resource modify id varchar(24);
alter table sys_menu_resource modify menu_id varchar(24);
alter table sys_menu_resource modify resource_id varchar(24);
alter table sys_menu_resource modify create_by varchar(24);
alter table sys_menu_resource modify update_by varchar(24);
alter table sys_menu_resource modify delete_by varchar(24);
alter table sys_node modify create_by varchar(24);
alter table sys_node modify update_by varchar(24);
alter table sys_node modify delete_by varchar(24);
alter table sys_node_load modify id varchar(24);
alter table sys_node_load modify update_by varchar(24);
alter table sys_node_load modify delete_by varchar(24);
alter table sys_oauth_client modify create_by varchar(24);
alter table sys_oauth_client modify update_by varchar(24);
alter table sys_oauth_client modify delete_by varchar(24);
alter table sys_p_category modify create_by varchar(24);
alter table sys_p_category modify update_by varchar(24);
alter table sys_p_category modify delete_by varchar(24);
alter table sys_p_message modify create_by varchar(24);
alter table sys_p_message modify update_by varchar(24);
alter table sys_p_message modify delete_by varchar(24);
alter table sys_p_message_notice modify create_by varchar(24);
alter table sys_p_message_notice modify update_by varchar(24);
alter table sys_p_message_notice modify delete_by varchar(24);
alter table sys_p_message_reminder modify create_by varchar(24);
alter table sys_p_message_reminder modify update_by varchar(24);
alter table sys_p_message_reminder modify delete_by varchar(24);
alter table sys_p_message_subscribe modify create_by varchar(24);
alter table sys_p_message_subscribe modify update_by varchar(24);
alter table sys_p_message_subscribe modify delete_by varchar(24);
alter table sys_page_info modify create_by varchar(24);
alter table sys_page_info modify update_by varchar(24);
alter table sys_page_info modify delete_by varchar(24);
alter table sys_page_info modify tenant_id varchar(24);
alter table sys_page_info_his modify create_by varchar(24);
alter table sys_page_info_his modify update_by varchar(24);
alter table sys_page_info_his modify delete_by varchar(24);
alter table sys_pay_app modify create_by varchar(24);
alter table sys_pay_app modify update_by varchar(24);
alter table sys_pay_app modify delete_by varchar(24);
alter table sys_pay_app modify tenant_id varchar(24);
alter table sys_pay_channel modify create_by varchar(24);
alter table sys_pay_channel modify update_by varchar(24);
alter table sys_pay_channel modify delete_by varchar(24);
alter table sys_pay_channel modify tenant_id varchar(24);
alter table sys_pay_merchant modify create_by varchar(24);
alter table sys_pay_merchant modify update_by varchar(24);
alter table sys_pay_merchant modify delete_by varchar(24);
alter table sys_pay_merchant modify tenant_id varchar(24);
alter table sys_pay_notify_log modify create_by varchar(24);
alter table sys_pay_notify_log modify update_by varchar(24);
alter table sys_pay_notify_log modify delete_by varchar(24);
alter table sys_pay_notify_log modify tenant_id varchar(24);
alter table sys_pay_notify_task modify create_by varchar(24);
alter table sys_pay_notify_task modify update_by varchar(24);
alter table sys_pay_notify_task modify delete_by varchar(24);
alter table sys_pay_notify_task modify tenant_id varchar(24);
alter table sys_pay_order modify create_by varchar(24);
alter table sys_pay_order modify update_by varchar(24);
alter table sys_pay_order modify delete_by varchar(24);
alter table sys_pay_order modify tenant_id varchar(24);
alter table sys_pay_order_ext modify create_by varchar(24);
alter table sys_pay_order_ext modify update_by varchar(24);
alter table sys_pay_order_ext modify delete_by varchar(24);
alter table sys_pay_order_ext modify tenant_id varchar(24);
alter table sys_pay_refund modify create_by varchar(24);
alter table sys_pay_refund modify update_by varchar(24);
alter table sys_pay_refund modify delete_by varchar(24);
alter table sys_pay_refund modify tenant_id varchar(24);
alter table sys_profile modify id varchar(24);
alter table sys_profile modify create_by varchar(24);
alter table sys_profile modify update_by varchar(24);
alter table sys_profile modify delete_by varchar(24);
alter table sys_report modify create_by varchar(24);
alter table sys_report modify update_by varchar(24);
alter table sys_report modify delete_by varchar(24);
alter table sys_report modify tenant_id varchar(24);
alter table sys_report_acl modify create_by varchar(24);
alter table sys_report_acl modify update_by varchar(24);
alter table sys_report_acl modify delete_by varchar(24);
alter table sys_report_acl modify tenant_id varchar(24);
alter table sys_report_category modify create_by varchar(24);
alter table sys_report_category modify update_by varchar(24);
alter table sys_report_category modify delete_by varchar(24);
alter table sys_report_category modify tenant_id varchar(24);
alter table sys_report_u_def modify create_by varchar(24);
alter table sys_report_u_def modify update_by varchar(24);
alter table sys_report_u_def modify delete_by varchar(24);
alter table sys_resourze modify id varchar(24);
alter table sys_resourze modify batch_id varchar(24);
alter table sys_resourze modify create_by varchar(24);
alter table sys_resourze modify update_by varchar(24);
alter table sys_resourze modify delete_by varchar(24);
alter table sys_resourze_cust modify id varchar(24);
alter table sys_resourze_cust modify batch_id varchar(24);
alter table sys_resourze_cust modify create_by varchar(24);
alter table sys_resourze_cust modify update_by varchar(24);
alter table sys_resourze_cust modify delete_by varchar(24);
alter table sys_role modify create_by varchar(24);
alter table sys_role modify update_by varchar(24);
alter table sys_role modify delete_by varchar(24);
alter table sys_role_menu modify id varchar(24);
alter table sys_role_menu modify role_id varchar(24);
alter table sys_role_menu modify menu_id varchar(24);
alter table sys_role_menu modify create_by varchar(24);
alter table sys_role_menu modify update_by varchar(24);
alter table sys_role_menu modify delete_by varchar(24);
alter table sys_role_user modify id varchar(24);
alter table sys_role_user modify role_id varchar(24);
alter table sys_role_user modify user_id varchar(24);
alter table sys_role_user modify create_by varchar(24);
alter table sys_role_user modify update_by varchar(24);
alter table sys_role_user modify delete_by varchar(24);
alter table sys_screen modify create_by varchar(24);
alter table sys_screen modify update_by varchar(24);
alter table sys_screen modify delete_by varchar(24);
alter table sys_screen modify tenant_id varchar(24);
alter table sys_screen_ds_api modify create_by varchar(24);
alter table sys_screen_ds_api modify update_by varchar(24);
alter table sys_screen_ds_api modify delete_by varchar(24);
alter table sys_screen_ds_api modify tenant_id varchar(24);
alter table sys_screen_ds_api_s modify create_by varchar(24);
alter table sys_screen_ds_api_s modify update_by varchar(24);
alter table sys_screen_ds_api_s modify delete_by varchar(24);
alter table sys_screen_ds_category modify create_by varchar(24);
alter table sys_screen_ds_category modify update_by varchar(24);
alter table sys_screen_ds_category modify delete_by varchar(24);
alter table sys_screen_ds_category modify tenant_id varchar(24);
alter table sys_screen_ds_data modify create_by varchar(24);
alter table sys_screen_ds_data modify update_by varchar(24);
alter table sys_screen_ds_data modify delete_by varchar(24);
alter table sys_screen_ds_data modify tenant_id varchar(24);
alter table sys_screen_ds_db modify create_by varchar(24);
alter table sys_screen_ds_db modify update_by varchar(24);
alter table sys_screen_ds_db modify delete_by varchar(24);
alter table sys_screen_ds_db modify tenant_id varchar(24);
alter table sys_screen_project modify create_by varchar(24);
alter table sys_screen_project modify update_by varchar(24);
alter table sys_screen_project modify delete_by varchar(24);
alter table sys_screen_project modify tenant_id varchar(24);
alter table sys_screen_project_data modify create_by varchar(24);
alter table sys_screen_project_data modify update_by varchar(24);
alter table sys_screen_project_data modify delete_by varchar(24);
alter table sys_sequence modify pk varchar(24);
alter table sys_sequence modify tenant_id varchar(24);
alter table sys_session_online modify user_id varchar(24);
alter table sys_session_online modify create_by varchar(24);
alter table sys_session_online modify update_by varchar(24);
alter table sys_session_online modify delete_by varchar(24);
alter table sys_sms_log modify template_id varchar(24);
alter table sys_sms_log modify user_id varchar(24);
alter table sys_sms_log modify tenant_id varchar(24);
alter table sys_sms_log modify create_by varchar(24);
alter table sys_sms_log modify update_by varchar(24);
alter table sys_sms_log modify delete_by varchar(24);
alter table sys_sms_record modify template_id varchar(24);
alter table sys_sms_record modify tenant_id varchar(24);
alter table sys_sms_record modify create_by varchar(24);
alter table sys_sms_record modify update_by varchar(24);
alter table sys_sms_record modify delete_by varchar(24);
alter table sys_sms_template modify id varchar(24);
alter table sys_sms_template modify tenant_id varchar(24);
alter table sys_sms_template modify create_by varchar(24);
alter table sys_sms_template modify update_by varchar(24);
alter table sys_sms_template modify delete_by varchar(24);
alter table sys_sms_tpl modify id varchar(24);
alter table sys_sms_tpl modify tenant_id varchar(24);
alter table sys_sms_tpl modify create_by varchar(24);
alter table sys_sms_tpl modify update_by varchar(24);
alter table sys_sms_tpl modify delete_by varchar(24);
alter table sys_sms_verification_code modify id varchar(24);
alter table sys_sms_verification_code modify topic varchar(24);
alter table sys_sms_verification_code modify user_id varchar(24);
alter table sys_sms_verification_code modify tenant_id varchar(24);
alter table sys_sms_verification_code modify create_by varchar(24);
alter table sys_sms_verification_code modify update_by varchar(24);
alter table sys_sms_verification_code modify delete_by varchar(24);
alter table sys_system_update modify id varchar(24);
alter table sys_system_update modify create_by varchar(24);
alter table sys_system_update modify update_by varchar(24);
alter table sys_system_update modify delete_by varchar(24);
alter table sys_tenant modify id varchar(24);
alter table sys_tenant modify company_id varchar(24);
alter table sys_tenant modify create_by varchar(24);
alter table sys_tenant modify update_by varchar(24);
alter table sys_tenant modify delete_by varchar(24);
alter table sys_test modify create_by varchar(24);
alter table sys_test modify update_by varchar(24);
alter table sys_test modify delete_by varchar(24);
alter table sys_test modify tenant_id varchar(24);
alter table sys_todo modify id varchar(24);
alter table sys_todo modify bill_id varchar(24);
alter table sys_todo modify create_by varchar(24);
alter table sys_todo modify update_by varchar(24);
alter table sys_todo modify delete_by varchar(24);
alter table sys_todo modify tenant_id varchar(24);
alter table sys_todo_category modify id varchar(24);
alter table sys_todo_category modify create_by varchar(24);
alter table sys_todo_category modify update_by varchar(24);
alter table sys_todo_category modify delete_by varchar(24);
alter table sys_todo_category modify tenant_id varchar(24);
alter table sys_todo_task modify id varchar(24);
alter table sys_todo_task modify todo_id varchar(24);
alter table sys_todo_task modify user_id varchar(24);
alter table sys_todo_task modify create_by varchar(24);
alter table sys_todo_task modify update_by varchar(24);
alter table sys_todo_task modify delete_by varchar(24);
alter table sys_todo_task modify tenant_id varchar(24);
alter table sys_token modify id varchar(24);
alter table sys_token modify user_id varchar(24);
alter table sys_token modify create_by varchar(24);
alter table sys_token modify update_by varchar(24);
alter table sys_token modify delete_by varchar(24);
alter table sys_tpl_file modify create_by varchar(24);
alter table sys_tpl_file modify update_by varchar(24);
alter table sys_tpl_file modify delete_by varchar(24);
alter table sys_tpl_file modify tenant_id varchar(24);
alter table sys_user modify id varchar(24);
alter table sys_user modify portrait_id varchar(24);
alter table sys_user modify create_by varchar(24);
alter table sys_user modify update_by varchar(24);
alter table sys_user modify delete_by varchar(24);
alter table sys_user_import modify id varchar(24);
alter table sys_user_import modify create_by varchar(24);
alter table sys_user_import modify update_by varchar(24);
alter table sys_user_import modify delete_by varchar(24);
alter table sys_user_keep modify id varchar(24);
alter table sys_user_tenant modify id varchar(24);
alter table sys_user_tenant modify owner_tenant_id varchar(24);
alter table sys_user_tenant modify create_by varchar(24);
alter table sys_user_tenant modify update_by varchar(24);
alter table sys_user_tenant modify delete_by varchar(24);
alter table v_eam_goods_stock_in modify create_by varchar(24);
alter table v_eam_goods_stock_in modify update_by varchar(24);
alter table v_eam_goods_stock_in modify delete_by varchar(24);
alter table v_eam_goods_stock_in modify tenant_id varchar(24);
alter table v_eam_goods_stock_out modify create_by varchar(24);
alter table v_eam_goods_stock_out modify update_by varchar(24);
alter table v_eam_goods_stock_out modify delete_by varchar(24);
alter table v_eam_goods_stock_out modify tenant_id varchar(24);
alter table v_hr_person modify create_by varchar(24);
alter table v_hr_person modify update_by varchar(24);
alter table v_hr_person modify delete_by varchar(24);
alter table v_hr_person modify tenant_id varchar(24);
alter table vehicle_a_select_item modify create_by varchar(24);
alter table vehicle_a_select_item modify update_by varchar(24);
alter table vehicle_a_select_item modify delete_by varchar(24);
alter table vehicle_apply modify create_by varchar(24);
alter table vehicle_apply modify update_by varchar(24);
alter table vehicle_apply modify delete_by varchar(24);
alter table vehicle_apply modify tenant_id varchar(24);
alter table vehicle_info modify create_by varchar(24);
alter table vehicle_info modify update_by varchar(24);
alter table vehicle_info modify delete_by varchar(24);
alter table vehicle_info modify tenant_id varchar(24);
alter table vehicle_insurance_company modify create_by varchar(24);
alter table vehicle_insurance_company modify update_by varchar(24);
alter table vehicle_insurance_company modify delete_by varchar(24);
alter table vehicle_insurance_company modify tenant_id varchar(24);
alter table vehicle_insurance_record modify create_by varchar(24);
alter table vehicle_insurance_record modify update_by varchar(24);
alter table vehicle_insurance_record modify delete_by varchar(24);
alter table vehicle_insurance_record modify tenant_id varchar(24);
alter table vehicle_m_select_item modify create_by varchar(24);
alter table vehicle_m_select_item modify update_by varchar(24);
alter table vehicle_m_select_item modify delete_by varchar(24);
alter table vehicle_maintenance modify create_by varchar(24);
alter table vehicle_maintenance modify update_by varchar(24);
alter table vehicle_maintenance modify delete_by varchar(24);
alter table vehicle_maintenance modify tenant_id varchar(24);
alter table vehicle_position modify create_by varchar(24);
alter table vehicle_position modify update_by varchar(24);
alter table vehicle_position modify delete_by varchar(24);
alter table vehicle_position modify tenant_id varchar(24);
alter table vehicle_select_item modify create_by varchar(24);
alter table vehicle_select_item modify update_by varchar(24);
alter table vehicle_select_item modify delete_by varchar(24);
alter table webfull_example_address modify id varchar(24);
alter table webfull_example_address modify create_by varchar(24);
alter table webfull_example_address modify update_by varchar(24);
alter table webfull_example_address modify delete_by varchar(24);
alter table webfull_example_goods modify id varchar(24);
alter table webfull_example_goods modify create_by varchar(24);
alter table webfull_example_goods modify update_by varchar(24);
alter table webfull_example_goods modify delete_by varchar(24);
alter table webfull_example_order modify id varchar(24);
alter table webfull_example_order modify address_id varchar(24);
alter table webfull_example_order modify create_by varchar(24);
alter table webfull_example_order modify update_by varchar(24);
alter table webfull_example_order modify delete_by varchar(24);
alter table webfull_example_order_item modify id varchar(24);
alter table webfull_example_order_item modify order_id varchar(24);
alter table webfull_example_order_item modify goods_id varchar(24);
alter table webfull_example_order_item modify create_by varchar(24);
alter table webfull_example_order_item modify update_by varchar(24);
alter table webfull_example_order_item modify delete_by varchar(24);
alter table webfull_example_reimbursement modify tenant_id varchar(24);
alter table webfull_example_reimbursement modify create_by varchar(24);
alter table webfull_example_reimbursement modify update_by varchar(24);
alter table webfull_example_reimbursement modify delete_by varchar(24);
alter table wms_area modify create_by varchar(24);
alter table wms_area modify update_by varchar(24);
alter table wms_area modify delete_by varchar(24);
alter table wms_area modify tenant_id varchar(24);
alter table wms_area_info modify create_by varchar(24);
alter table wms_area_info modify update_by varchar(24);
alter table wms_area_info modify delete_by varchar(24);
alter table wms_area_info modify tenant_id varchar(24);
alter table wms_business_type modify create_by varchar(24);
alter table wms_business_type modify update_by varchar(24);
alter table wms_business_type modify delete_by varchar(24);
alter table wms_business_type modify tenant_id varchar(24);
alter table wms_car modify create_by varchar(24);
alter table wms_car modify update_by varchar(24);
alter table wms_car modify delete_by varchar(24);
alter table wms_car modify tenant_id varchar(24);
alter table wms_city_type modify create_by varchar(24);
alter table wms_city_type modify update_by varchar(24);
alter table wms_city_type modify delete_by varchar(24);
alter table wms_city_type modify tenant_id varchar(24);
alter table wms_company_attr modify create_by varchar(24);
alter table wms_company_attr modify update_by varchar(24);
alter table wms_company_attr modify delete_by varchar(24);
alter table wms_company_attr modify tenant_id varchar(24);
alter table wms_company_level modify create_by varchar(24);
alter table wms_company_level modify update_by varchar(24);
alter table wms_company_level modify delete_by varchar(24);
alter table wms_company_level modify tenant_id varchar(24);
alter table wms_company_status modify create_by varchar(24);
alter table wms_company_status modify update_by varchar(24);
alter table wms_company_status modify delete_by varchar(24);
alter table wms_company_status modify tenant_id varchar(24);
alter table wms_company_trade modify create_by varchar(24);
alter table wms_company_trade modify update_by varchar(24);
alter table wms_company_trade modify delete_by varchar(24);
alter table wms_company_trade modify tenant_id varchar(24);
alter table wms_customer modify create_by varchar(24);
alter table wms_customer modify update_by varchar(24);
alter table wms_customer modify delete_by varchar(24);
alter table wms_customer modify tenant_id varchar(24);
alter table wms_delivery_method modify create_by varchar(24);
alter table wms_delivery_method modify update_by varchar(24);
alter table wms_delivery_method modify delete_by varchar(24);
alter table wms_delivery_method modify tenant_id varchar(24);
alter table wms_loc_type modify create_by varchar(24);
alter table wms_loc_type modify update_by varchar(24);
alter table wms_loc_type modify delete_by varchar(24);
alter table wms_loc_type modify tenant_id varchar(24);
alter table wms_oper_type modify create_by varchar(24);
alter table wms_oper_type modify update_by varchar(24);
alter table wms_oper_type modify delete_by varchar(24);
alter table wms_oper_type modify tenant_id varchar(24);
alter table wms_order_type modify create_by varchar(24);
alter table wms_order_type modify update_by varchar(24);
alter table wms_order_type modify delete_by varchar(24);
alter table wms_order_type modify tenant_id varchar(24);
alter table wms_product modify create_by varchar(24);
alter table wms_product modify update_by varchar(24);
alter table wms_product modify delete_by varchar(24);
alter table wms_product modify tenant_id varchar(24);
alter table wms_product_attr modify create_by varchar(24);
alter table wms_product_attr modify update_by varchar(24);
alter table wms_product_attr modify delete_by varchar(24);
alter table wms_product_attr modify tenant_id varchar(24);
alter table wms_product_category modify create_by varchar(24);
alter table wms_product_category modify update_by varchar(24);
alter table wms_product_category modify delete_by varchar(24);
alter table wms_product_category modify tenant_id varchar(24);
alter table wms_receive_method modify create_by varchar(24);
alter table wms_receive_method modify update_by varchar(24);
alter table wms_receive_method modify delete_by varchar(24);
alter table wms_receive_method modify tenant_id varchar(24);
alter table wms_region modify create_by varchar(24);
alter table wms_region modify update_by varchar(24);
alter table wms_region modify delete_by varchar(24);
alter table wms_region modify tenant_id varchar(24);
alter table wms_storehouse modify create_by varchar(24);
alter table wms_storehouse modify update_by varchar(24);
alter table wms_storehouse modify delete_by varchar(24);
alter table wms_storehouse modify tenant_id varchar(24);
alter table wms_storehouse_attr_v modify create_by varchar(24);
alter table wms_storehouse_attr_v modify update_by varchar(24);
alter table wms_storehouse_attr_v modify delete_by varchar(24);
alter table wms_storehouse_attr_v modify tenant_id varchar(24);
alter table wms_storelocation modify create_by varchar(24);
alter table wms_storelocation modify update_by varchar(24);
alter table wms_storelocation modify delete_by varchar(24);
alter table wms_storelocation modify tenant_id varchar(24);
alter table wms_supplier modify create_by varchar(24);
alter table wms_supplier modify update_by varchar(24);
alter table wms_supplier modify delete_by varchar(24);
alter table wms_supplier modify tenant_id varchar(24);
alter table wms_temperature_attribute modify create_by varchar(24);
alter table wms_temperature_attribute modify update_by varchar(24);
alter table wms_temperature_attribute modify delete_by varchar(24);
alter table wms_temperature_attribute modify tenant_id varchar(24);
alter table wms_transport_addr modify create_by varchar(24);
alter table wms_transport_addr modify update_by varchar(24);
alter table wms_transport_addr modify delete_by varchar(24);
alter table wms_transport_addr modify tenant_id varchar(24);
alter table wms_unit modify create_by varchar(24);
alter table wms_unit modify update_by varchar(24);
alter table wms_unit modify delete_by varchar(24);
alter table wms_unit modify tenant_id varchar(24);
alter table wms_work_type modify create_by varchar(24);
alter table wms_work_type modify update_by varchar(24);
alter table wms_work_type modify delete_by varchar(24);
alter table wms_work_type modify tenant_id varchar(24);
alter table wo_category modify create_by varchar(24);
alter table wo_category modify update_by varchar(24);
alter table wo_category modify delete_by varchar(24);
alter table wo_network_strategy_apply modify create_by varchar(24);
alter table wo_network_strategy_apply modify update_by varchar(24);
alter table wo_network_strategy_apply modify delete_by varchar(24);
alter table wo_network_strategy_apply modify tenant_id varchar(24);
alter table wo_network_strategy_info modify create_by varchar(24);
alter table wo_network_strategy_info modify update_by varchar(24);
alter table wo_network_strategy_info modify delete_by varchar(24);
alter table wo_server_apply modify create_by varchar(24);
alter table wo_server_apply modify update_by varchar(24);
alter table wo_server_apply modify delete_by varchar(24);
alter table wo_server_apply modify tenant_id varchar(24);
alter table wo_server_info modify create_by varchar(24);
alter table wo_server_info modify update_by varchar(24);
alter table wo_server_info modify delete_by varchar(24);
alter table wo_server_os_type modify create_by varchar(24);
alter table wo_server_os_type modify update_by varchar(24);
alter table wo_server_os_type modify delete_by varchar(24);
alter table wo_server_os_type modify tenant_id varchar(24);
alter table wo_service_desk modify create_by varchar(24);
alter table wo_service_desk modify update_by varchar(24);
alter table wo_service_desk modify delete_by varchar(24);
alter table wo_service_desk modify tenant_id varchar(24);
alter table wo_service_my_portal modify create_by varchar(24);
alter table wo_service_my_portal modify update_by varchar(24);
alter table wo_service_my_portal modify delete_by varchar(24);
alter table wo_service_my_portal modify tenant_id varchar(24);
alter table wo_service_portal modify create_by varchar(24);
alter table wo_service_portal modify update_by varchar(24);
alter table wo_service_portal modify delete_by varchar(24);
alter table wo_service_portal modify tenant_id varchar(24);
alter table wo_slb_strategy_apply modify create_by varchar(24);
alter table wo_slb_strategy_apply modify update_by varchar(24);
alter table wo_slb_strategy_apply modify delete_by varchar(24);
alter table wo_slb_strategy_apply modify tenant_id varchar(24);
alter table wo_slb_strategy_info modify create_by varchar(24);
alter table wo_slb_strategy_info modify update_by varchar(24);
alter table wo_slb_strategy_info modify delete_by varchar(24);