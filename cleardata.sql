-- update sys_lang set deleted=5 where context='menu';
delete from act_ru_event_callback where 1=1;
update sys_config set value='["eam_sq"]' where code='system.version.applyType';
update sys_config set value='yes' where code='system.version.applyTypeStatus';
delete from sys_lang where context='menu';
delete from sys_lang where context='menu:d1';
delete from sys_lang where context='layui';
delete from sys_lang where context='result:solution';
delete from sys_lang where context='result:message';
delete from sys_lang where defaults like '已选择%个权限项';
delete from sys_lang where defaults like '新菜单%';
delete from sys_lang where defaults like 'EAM_%';
update sys_config set value='yes' where code='system.version.applyTypeStatus';
delete from sys_job where id='sys_version_type';
delete from sys_p_message where 1=1;
delete from sys_p_message_subscribe where 1=1;
delete from eam_asset_oper_lock where 1=1;
delete from magic_cron_log where 1=1;
delete from sys_invoke_log where 1=1;
delete from magic_cron_log where 1=1;
delete from sys_licence_switch where licence_tab in ('sys_licence_free_oa','sys_licence_free_hr');
delete from oa_banner where 1=1;
INSERT INTO oa_banner(`id`, `name`, `status`, `picture_id`, `url`, `sort`, `create_by`, `create_time`, `update_by`, `update_time`, `deleted`, `delete_by`, `delete_time`, `version`, `tenant_id`)
VALUES
('708057333552381952', 'banner2 默认', 'enable', 'T001_image_banner2', '/business/oa/portal/banner.png', '2', '110588348101165911', '2023-05-08 20:47:03', '110588348101165911', '2023-05-26 13:17:37', '0', NULL, NULL, '7', 'T001'),
('714466896212131841', 'banner1 默认', 'enable', 'T001_image_banner1', '/business/oa/portal/banner.png', '1', '110588348101165911', '2023-05-26 13:16:22', '110588348101165911', '2023-05-26 13:17:29', '0', NULL, NULL, '3', 'T001');
-- oa
delete from oa_notice where iftop='N';
delete from oa_work_rpt where 1=1;
delete from oa_schedule_plan where 1=1;
-- system
delete from sys_token where 1=1;
delete from hrm_company where id<>'001';
delete from sys_tenant where id<>'T001';
update sys_tenant set valid=1 where id='T001';
delete from sys_dict where deleted<>0;
delete from sys_dict_item where deleted<>0;
update sys_dict set create_time=now();
update sys_dict_item set create_time=now();
delete from sys_config where deleted<>0;
delete from sys_job where deleted<>0;
delete from sys_job_log where 1=1;
delete from sys_tenant where deleted=1;
delete from sys_code_allocation where deleted=1;
delete from sys_code_attr where deleted=1;
delete from sys_code_register where deleted=1;
delete from sys_code_rule where deleted=1;
delete from sys_tpl_file where deleted=1;
delete from sys_backup_db where 1=1;
delete from sys_sms_record where 1=1;
-- system time
-- update sys_config set create_time=null,create_by=null;
-- update sys_dict_item set create_time=null,create_by=null;
-- update sys_dict set create_time=null,create_by=null;
update sys_role set create_time=null,create_by=null;
update sys_busi_role set create_time=null,create_by=null;
update sys_tenant set create_time=null,create_by=null;
update hrm_company set create_time=null,create_by=null;
update sys_job set create_time=null,create_by=null;
update sys_code_allocation set create_time=null,create_by=null;
update sys_code_attr set create_time=null,create_by=null;
update sys_code_register set create_time=null,create_by=null;
update sys_code_rule set create_time=null,create_by=null;
update sys_tpl_file set create_time=null,create_by=null;
update sys_licence_switch set create_time=null,create_by=null;
-- eam
delete from eam_asset where 1=1    ;
delete from eam_asset_item where 1=1;
delete from eam_asset_batch where 1=1;
delete from eam_asset_process_record where 1=1;
delete from eam_asset_selected_data where 1=1;
delete from eam_asset_data_change where 1=1    ;
delete from eam_asset_repair where 1=1    ;
delete from eam_asset_scrap where 1=1    ;
delete from eam_asset_allocation where 1=1    ;
delete from eam_asset_borrow where 1=1    ;
delete from eam_failure_registration where 1=1   ;
delete from eam_asset_borrow_return where 1=1   ;
delete from eam_asset_collection where 1=1    ;
delete from eam_asset_collection_return where 1=1    ;
delete from eam_asset_handle where 1=1    ;
delete from eam_asset_tranfer where 1=1    ;
delete from eam_asset_storage where 1=1    ;
delete from eam_purchase_apply  where 1=1    ;
delete from eam_purchase_apply_item  where 1=1    ;
delete from eam_purchase_check  where 1=1    ;
delete from eam_purchase_order_detail  where 1=1    ;
delete from eam_asset_storage  where 1=1    ;
delete from eam_purchase_order  where 1=1    ;
delete from eam_mapping_owner where 1=1;
delete from eam_c1_mapping where 1=1;
-- delete from eam_asset_depreciation  where 1=1;
-- eam_asset_depreciation
delete from eam_asset_depreciation_oper  where 1=1;
delete from eam_asset_depreciation_detail  where 1=1;
delete from eam_asset_depreciation_detail  where 1=1;
delete from eam_asset_depreciation_exclude  where 1=1;
delete from eam_asset_maintenance_update  where 1=1;
-- software
delete from eam_asset_software  where 1=1    ;
delete from eam_asset_software_change  where 1=1    ;
delete from eam_asset_software_change_detail  where 1=1 ;
delete from eam_asset_software_distribute  where 1=1    ;
delete from eam_asset_software_distribute_data  where 1=1 ;
delete from eam_asset_software_maintenance  where 1=1    ;
delete from eam_asset_software_maintenance_detail  where 1=1;
-- stock
delete from eam_asset_stock_deliver  where 1=1   ;
delete from eam_asset_stock_collection  where 1=1   ;
delete from eam_asset_stock_goods  where 1=1   ;
delete from eam_asset_stock_goods_adjust  where 1=1   ;
delete from eam_asset_stock_goods_detail  where 1=1  ;
delete from eam_asset_stock_goods_in  where 1=1   ;
delete from eam_asset_stock_goods_out  where 1=1   ;
delete from eam_asset_stock_goods_tranfer  where 1=1   ;
delete from eam_asset_stock_goods_use  where 1=1   ;
delete from eam_goods_stock_usage  where 1=1;
delete from eam_goods_stock where owner_code<>'goods';
-- inventory
delete from eam_inventory where 1=1   ;
delete from eam_inventory_asset where 1=1 ;
delete from eam_inventory_director where 1=1 ;
delete from eam_inventory_manager where 1=1 ;
delete from eam_inventory_user where 1=1 ;
delete from eam_inventory_plan where 1=1   ;
delete from eam_group_user where 1=1;
-- eam_maintain
delete from eam_maintain_plan where tenant_id='T001';
delete from eam_maintain_group where tenant_id='T001';
delete from eam_maintain_project where tenant_id='T001';
delete from eam_maintain_project_select where 1=1;
delete from eam_maintain_task where tenant_id='T001';
delete from eam_maintain_task_project where 1=1;
delete from eam_purchase_import where 1=1;
delete from eam_stock_import where 1=1;
-- repair
-- delete from eam_repair_category where tenant_id='T001';
-- delete from eam_repair_category_tpl where tenant_id='T001';
delete from eam_repair_group  where tenant_id='T001';
delete from eam_repair_order  where tenant_id='T001';
delete from eam_repair_order_acceptance  where tenant_id='T001';
delete from eam_repair_order_act  where tenant_id='T001';
delete from eam_repair_rule  where tenant_id='T001';
delete from eam_repair_rule_item where 1=1;
delete from eam_stock_inventory_plan where 1=1;
delete from eam_stock_inventory_task where 1=1;
delete from eam_stock_inventory_asset where 1=1;
-- inspection
-- delete from eam_inspection_group where tenant_id='T001';
delete from eam_inspection_group_user where 1=1;
delete from eam_inspection_plan  where tenant_id='T001';
delete from eam_inspection_plan_point  where tenant_id='T001';
-- delete from eam_inspection_point  where tenant_id='T001';
delete from eam_inspection_point_owner  where tenant_id='T001';
-- delete from eam_inspection_route  where tenant_id='T001';
delete from eam_inspection_task  where tenant_id='T001';
delete from eam_inspection_task_point  where 1=1;
-- stock
delete from eam_stock where 1=1   ;
delete from eam_asset_stock_collection where 1=1;
-- asset rack
-- delete from eam_asset_rack where 1=1;
-- delete from eam_asset_rack_info where 1=1;
-- cont
delete from cont_contract where 1=1   ;
delete from cont_contract_attachment where 1=1    ;
delete from cont_contract_performance where 1=1    ;
delete from cont_contract_signer where 1=1   ;
delete from cont_contract_archive where 1=1   ;
delete from cont_contract_collect where 1=1   ;
delete from cont_contract_invoice where 1=1   ;
delete from cont_contract_pay where 1=1   ;
delete from cont_contract_collect where 1=1   ;
-- maintenance
delete from eam_asset_maintenance_record where 1=1;
delete from eam_asset_maintenance_record_u where 1=1;
delete from eam_asset_maintenance_update where 1=1;
-- other
delete from eam_feedback where 1=1 ;
-- job
delete from sys_job_log where 1=1;
-- kn
delete from kn_content where 1=1   ;
-- ops
delete from ops_host where 1=1   ;
delete from ops_host_db where 1=1 ;
delete from ops_host_mid where 1=1;
delete from ops_host_os where 1=1;
delete from ops_information_system where 1=1   ;
delete from ops_db_instance where 1=1   ;
delete from ops_voucher where 1=1   ;
delete from ops_voucher_history where 1=1   ;
delete from ops_voucher_owner where 1=1   ;
delete from ops_voucher_priv where 1=1   ;
delete from ops_person where 1=1   ;
delete from ops_ip_address_range where 1=1;
delete from ops_system_console_info where 1=1;
delete from ops_ciphertext_history where 1=1;
delete from ops_ciphertext_box_data where 1=1;
delete from ops_ciphertext_box where deleted=1;
delete from ops_ciphertext_conf where deleted=1;
delete from ops_ciphertext_conf where user_id<>'E001';
delete from ops_db_info where 1=1;
delete from ops_db_backup_record where 1=1;
delete from ops_db_backup_log where 1=1;
-- ops_certificate
delete from ops_certificate where 1=1;
-- eam employ
delete from eam_asset_employee_apply where 1=1   ;
delete from eam_asset_employee_handover where 1=1   ;
delete from eam_asset_employee_loss where 1=1   ;
delete from eam_asset_employee_repair where 1=1   ;
delete from eam_device_sp where 1=1   ;
delete from eam_work_order where 1=1;
-- workorder
delete from wo_network_strategy_apply where 1=1   ;
delete from wo_network_strategy_info ;
delete from wo_server_apply where 1=1   ;
delete from wo_server_info where 1=1;
delete from wo_slb_strategy_apply where 1=1   ;
delete from wo_slb_strategy_info where 1=1;
-- vehicle
delete from vehicle_a_select_item where 1=1;
delete from vehicle_apply where 1=1;
delete from vehicle_info where 1=1;
delete from vehicle_insurance_record where 1=1;
delete from vehicle_m_select_item where 1=1;
delete from vehicle_maintenance where 1=1;
delete from vehicle_select_item where 1=1;
-- BPM
delete from bpm_demo_leave;
delete from bpm_demo_common;
delete from bpm_form_instance;
delete from bpm_form_instance_bill;
delete from bpm_process_instance;
delete from bpm_process_error;
delete from bpm_process_definition_deploy;
delete from bpm_process_definition_node_assignee where node_id not in (select id from bpm_process_definition_node where process_definition_file_id in (select id from bpm_process_definition_file where activated=1));
delete from bpm_process_definition_node where process_definition_file_id in (select id from bpm_process_definition_file where activated=0);
delete from bpm_process_definition_file where activated=0;
delete from bpm_form_definition where deleted=1;
delete from bpm_process_instance_remind where 1=1;
delete from bpm_process_instance_remind_receiver where 1=1;
-- bpm
delete from bpm_form_instance where 1=1;
delete from bpm_form_instance_bill where 1=1;
delete from bpm_process_instance where 1=1;
delete from bpm_task where 1=1;
delete from bpm_task_approval where 1=1;
delete from bpm_task_approval_attachment where 1=1;
delete from bpm_task_assignee where 1=1;
delete from bpm_task_read where 1=1;
delete from bpm_user_statistics where 1=1;
-- PCM
delete from pcm_catalog where id='484764974338543617';
delete from pcm_catalog where id='484764976855126017';
-- HRM
delete from hr_assessment_task where 1=1;
delete from hr_attendance_data_process  where 1=1;
delete from hr_attendance_data  where 1=1;
delete from hr_attendance_overtime  where 1=1;
delete from hr_attendance_holiday  where 1=1;
delete from hr_attendance_official_busi  where 1=1;
delete from hr_attendance_record  where 1=1;
delete from hr_person_absence_apply  where 1=1;
delete from hr_person_official_business  where 1=1;
delete from hr_person_confirm_apply  where 1=1;
delete from hr_person_leave  where 1=1;
delete from hr_person_income_certificate_apply  where 1=1;
delete from hr_assessment_plan  where 1=1;
delete from hr_salary  where 1=1;
delete from hr_personnel_requirement_apply where 1=1;
delete from hr_recruitment_plan_apply where 1=1;
delete from hr_person_store where 1=1;
delete from hr_assessment_bill_task where 1=1;
delete from hr_person_work_experience where 1=1;
delete from hr_person_certificate where 1=1;
delete from hr_person_social_relation where 1=1;
delete from hr_paper_task where 1=1;;
delete from hr_learn_task where 1=1;;
delete from hr_person  where 1=1;
delete from hr_person_contract  where 1=1;
delete from hr_salary_action  where 1=1;
delete from hr_person_resume  where 1=1;
delete from hr_person_attendance where 1=1;
delete from hr_person_attendance_money where 1=1;
delete from hr_person_attendance_rec where 1=1;
delete from hr_person_cert where 1=1;
delete from hr_person_file where 1=1;
delete from hr_person_file_out where 1=1;
delete from hrm_employee  where deleted=1;
delete from hrm_person  where deleted=1;
delete from hrm_company  where deleted=1;
delete from hrm_organization  where deleted=1;
delete from hrm_position  where deleted=1;
delete from hrm_employee_position  where deleted=1;
delete from hrm_favourite_group  where deleted=1;
delete from hrm_favourite_group_item  where deleted=1;
-- ops auto
delete from ops_auto_batch  where 1=1;
delete from ops_auto_batch_node  where  1=1;
delete from ops_auto_group  where  1=1;
delete from ops_auto_node  where  1=1;
delete from ops_auto_node_select  where  1=1;
delete from ops_auto_task  where  1=1;
delete from ops_auto_task_log  where  1=1;
delete from ops_auto_task_m_log  where  1=1;
delete from ops_auto_task_node  where  1=1;
delete from ops_auto_voucher  where 1=1;
delete from ops_monitor_node  where 1=1;
-- ops auto deploy
delete from ops_auto_action_file where id not like 'T001%';
delete from ops_auto_action_script where id not like 'T001%';
delete from ops_software_media where 1=1;
delete from ops_auto_node where 1=1;
delete from ops_auto_action where id not like 'T001%';
-- custom middle table
delete from eam_c1_mapping where 1=1;
delete from eam_c1_qh_fa_additions where 1=1;
delete from eam_c1_sync_asset_record where 1=1;
delete from eam_c1_sync_asset where 1=1;
delete from sys_log_collect where 1=1;
delete from sys_material_resource where 1=1;
-- clear hrm
-- delete from hrm_organization where id not in ('2');
delete from sys_user where deleted=1;
delete from sys_user_tenant where deleted=1;
delete from hrm_employee where deleted=1;
delete from hrm_person where deleted=1;
delete from sys_user where account not in (select value from sys_user_keep);
delete from sys_user_tenant where user_id not in (select id from sys_user);
delete from hrm_employee where id not in (select employee_id from sys_user_tenant);
delete from hrm_person where id not in (select person_id from hrm_employee);
-- clear hr
delete from hr_person where deleted=1;
delete from hr_person_contract where 1=1;
delete from sys_user_import where 1=1;
-- check data
-- select * from sys_user a,sys_user_tenant b,hrm_employee c,hrm_person d,hrm_employee_position f where a.id=b.user_id and b.employee_id=c.id and c.person_id=d.id and f.employee_id=c.id
delete from sys_user where deleted=1;
delete from sys_user_tenant where deleted=1;
delete from hrm_employee where deleted=1;
delete from hrm_person where deleted=1;
delete from sys_role_user where deleted=1;
delete from hrm_employee_position where deleted=1;
delete from hrm_position where deleted=1;
-- user
update sys_user set portrait_id='T001_image_head';
delete from sys_user_tenant where user_id not in (select id from sys_user);
delete from hrm_employee where id not in ( select employee_id from sys_user_tenant);
delete from hrm_person where id not in (select person_id from hrm_employee);
delete from sys_role_user where user_id not in (select id from sys_user);
delete from hrm_employee_position where employee_id not in (select id from hrm_employee);
-- netdisk
delete from oa_visitor_record where 1=1;
delete from oa_fin_accounting_record where 1=1;
delete from oa_netdisk_resource_limit where user_id<>'default';
delete from oa_netdisk_file where 1=1;
delete from oa_netdisk_folder where 1=1;
delete from oa_netdisk_origin_file where 1=1;
delete from oa_netdisk_recycle where 1=1;
delete from oa_netdisk_share_me where 1=1;
delete from oa_netdisk_virtual_fd where 1=1;
delete from oa_netdisk_my_share where 1=1;
delete from oa_netdisk_my_favorite where 1=1;
delete from oa_fin_invoice_information where 1=1;
delete from oa_fin_collect_ticket where 1=1;
delete from oa_fin_reimburse_apply where 1=1;
delete from oa_fin_reimburse_item where 1=1;
delete from oa_fin_collection_no_ticket where 1=1;
delete from oa_fin_collection_record where 1=1;
delete from oa_fin_company_subject where 1=1;
delete from oa_fin_invoicing_apply where 1=1;
delete from oa_fin_payment_no_ticket where 1=1;
delete from oa_fin_payment_record where 1=1;
delete from oa_project where code not in ('eam_asset_repair');
delete from oa_project_doc where 1=1;
delete from oa_project_phase where project_id not in (select id from oa_project where code  in ('eam_asset_repair')) and project_id not like 'oa_project_phase_tpl%';
delete from oa_project_task where project_id not in (select id from oa_project where code  in ('eam_asset_repair'));
delete from oa_project_user where project_id not in (select id from oa_project where code  in ('eam_asset_repair'));
delete from oa_project_task_rcd where 1=1;
delete from sys_mapping_owner where owner not in ('build_in','flow_status');
delete from sys_mapping_owner where selected_code='sys_config_value';
delete from ops_file_value where 1=1;
-- delete
update sys_user set portrait_id ='T001_image_head' where 1=1;
-- select * from sys_user a,sys_user_tenant b,hrm_employee c,hrm_person d,hrm_employee_position f where a.id=b.user_id and b.employee_id=c.id and c.person_id=d.id and f.employee_id=c.id
delete from sys_user where deleted=1;
delete from sys_user_tenant where deleted=1;
delete from hrm_employee where deleted=1;
delete from hrm_person where deleted=1;
delete from sys_role_user where deleted=1;
delete from hrm_employee_position where deleted=1;
delete from hrm_position where deleted=1;
delete from hrm_position where org_id not in (select id from hrm_organization);
-- user
update sys_user set portrait_id='T001_image_head';
delete from sys_user_tenant where user_id not in (select id from sys_user);
delete from hrm_employee where id not in ( select employee_id from sys_user_tenant);
delete from hrm_person where id not in (select person_id from hrm_employee);
delete from sys_role_user where user_id not in (select id from sys_user);
delete from hrm_employee_position where employee_id not in (select id from hrm_employee);
-- menu
update sys_menu set hidden=1 where hierarchy like '%/734474163506380800%';
delete from sys_menu where ( label ='导出' or label='导入' ) and authority like 'wms_%';
delete from sys_menu where ( label ='导出' or label='导入' ) and authority like 'sys_pay%';
delete from sys_menu where ( label ='导出' or label='导入' ) and authority like 'ops_%';
delete from sys_menu where ( label ='导出' or label='导入' ) and authority like 'sys_db_cache%';
delete from sys_menu where ( label ='导出' or label='导入' ) and authority like 'sys_resourze%';
delete from sys_menu where ( label ='导出' or label='导入' ) and authority like 'sys_menu%';
delete from sys_menu where ( label ='导出' or label='导入' ) and authority like 'sys_role%';
delete from sys_menu where ( label ='导出' or label='导入' ) and authority like 'sys_user%';
delete from sys_menu where ( label ='导出' or label='导入' ) and authority like 'sys_config%';
delete from sys_menu where ( label ='导出' or label='导入' ) and authority like 'sys_session_online%';
delete from sys_menu where ( label ='导出' or label='导入' ) and authority like 'sys_tenant%';
delete from sys_menu where ( label ='导出' or label='导入' ) and authority like 'sys_key_code%';
delete from sys_menu where ( label ='导出' or label='导入' ) and authority like 'sys_node%';
delete from sys_menu where ( label ='导出' or label='导入' ) and authority like 'sys_dashboard%';
delete from sys_menu where ( label ='导出' or label='导入' ) and authority like 'oa_netdisk%';
delete from sys_menu where ( label ='导出' or label='导入' ) and authority like 'oa_crm%';
delete from sys_menu where ( label ='导出' or label='导入' ) and authority like 'iot_%';
delete from sys_menu where ( label ='导出' or label='导入' ) and authority like 'eam_c1%';
delete from sys_menu where ( label ='导出' or label='导入' ) and authority like 'oa_%';
-- delete from sys_role_menu where menu_id in (select id from sys_menu where hierarchy like '%/734474163506380800%' and type='page');
-- delete from sys_role_menu where menu_id in (select id from sys_menu where hierarchy like '%/734474163506380800%' and type='folder');
-- delete from sys_role_menu where menu_id in (select id from sys_menu where hierarchy like '%/734474163506380800%' and type='function' and label in ('查询列表','查看表单'));
delete from sys_menu where deleted=1;
delete from sys_menu where deleted=1;
delete from sys_menu_resource where menu_id not in (select id from sys_menu);
delete from sys_menu_resource where resource_id not in (select id from sys_resourze);
-- role
-- hidden inter
delete from sys_role where deleted=1;
delete from sys_role_menu where menu_id not in (select id from sys_menu where deleted=0);
delete from sys_menu_resource where menu_id not in (select id from sys_menu);
delete from sys_role where deleted<>0;
delete from sys_role where name like '测试%';
delete from sys_role_menu where menu_id in (select id from sys_menu where hierarchy like '472036556115279872%');
delete from sys_role_menu where role_id not in (select id from sys_role);
delete from sys_role_user where role_id not in (select id from sys_role);
delete from sys_busi_role where deleted<>0;
delete from sys_busi_role_member where role_id not in (select id from sys_busi_role);
delete from sys_busi_role_member where 1=1;
delete from sys_role_menu where role_id not in (select id from sys_role);
delete from sys_role_menu where menu_id not in (select id from sys_menu);
commit;
update bpm_process_definition set valid=0 where code in
                                                ('sys_bpm_common',
                                                 'eam_asset_collection_return',
                                                 'eam_asset_repair_order',
                                                 'eam_asset_collection',
                                                 'eam_asset_borrow',
                                                 'eam_asset_allocate',
                                                 'eam_asset_tranfer',
                                                 'eam_asset_storage',
                                                 'foxnic-leave',
                                                 'payment-process',
                                                 'p-expense-all',
                                                 'a-expense-all',
                                                 'p-expense-any',
                                                 'a-expense-any',
                                                 'fetchback-jump',
                                                 'clps-demo',
                                                 'cst_contract_apply',
                                                 'draft-test',
                                                 'webfull-reimbursement',
                                                 'demo-busi-case',
                                                 'sys_bpm_common',
                                                 'bpm_common_action1',
                                                 'bpm_common_action2',
                                                 '----end-----'
                                                    );
-- 优化内部引用
-- delete from sys_menu where hierarchy like '734474163506380800%';
commit;