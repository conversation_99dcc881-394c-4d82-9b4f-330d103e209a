#!/bin/sh
#################################################################################
# put /app/app/bpm目录下
#
#################################################################################
cur_dir=$(cd `dirname $0`; pwd)
cd $cur_dir

docker_image_version=2.9.2
if [[ -n $1 ]];then
  docker_image_version=$1
fi
docker_image_flag=fapp_bpm_${docker_image_version}
echo "docker image:$docker_image_flag"

###### 修改配置文件
nginx_ip=***********
bpm_ip=***********
app_ip=***********
db_ip=***********
redis_ip=***********

app_port=8089
bpm_port=8099
db_port=3306
db_name=app
db_user=root
db_pwd=P@ssw0rd123456

##修改application.yml文件
application_yml=application_tpl_docker.yml
if [[ ! -f application_tpl.yml ]];then
  echo "application_tpl.yml not found"
  exit 1
fi
cat application_tpl.yml>$application_yml

cat << EOF > entrypoint.sh
#!/bin/bash

bpm_ip=127.0.0.1
bpm_port=8099
app_port=8089
app_ip=\`env|grep APP_HOST_IP|awk -F '=' '{print \$NF}'\`
db_ip=\`env|grep MYSQL_HOST_IP|awk -F '=' '{print \$NF}'\`
db_ip_cnt=`cat /etc/hosts|grep db.app.dt|wc -l`
if [ \$db_ip_cnt -gt 0 ];then
  db_ip=db.app.dt
fi
db_port=\`env|grep MYSQL_HOST_PORT|awk -F '=' '{print \$NF}'\`
db_name=\`env|grep MYSQL_DATABASE|awk -F '=' '{print \$NF}'\`
db_user=\`env|grep MYSQL_USER|awk -F '=' '{print \$NF}'\`
db_pwd=\`env|grep MYSQL_PASSWORD|awk -F '=' '{print \$NF}'\`

sed -i "s/APP_WEB_PORT/\$app_port/g"                          /app/app/bpm/application.yml
sed -i "s/APP_DB_PORT/\$db_port/g"                            /app/app/bpm/application.yml
sed -i "s/APP_DB_NAME/\$db_name/g"                            /app/app/bpm/application.yml
sed -i "s/APP_DB_USERNAME/\$db_user/g"                        /app/app/bpm/application.yml
sed -i "s/APP_DB_PASSWORD/\$db_pwd/g"                         /app/app/bpm/application.yml
sed -i "s/127.0.0.1:3306/\${db_ip}:\${db_port}/g"             /app/app/bpm/application.yml
sed -i "s/127.0.0.1:8089/\${app_ip}:\${app_port}/g"           /app/app/bpm/application.yml
sed -i "s/127.0.0.1:8099/\${bpm_ip}:\${bpm_port}/g"           /app/app/bpm/application.yml

touch cmd.conf
echo "************************************************************************************************************************************************************************************************************* count(1) cnt from information_schema.tables where table_schema @eq@ '\$db_name' and table_name like '%sys_%'">/mysql.node
#connect check
while true
do
  java -jar /ops.jar -n /mysql.node -mysql connectCheck >/tmp/connect.log
  connectCnt=\`cat /tmp/connect.log |grep 20|wc -l\`
  echo "Current Connect Status:\$connectCnt"
  if [ \$connectCnt -gt 0 ]; then
    echo "mysql connect success!"
    break;
  fi
  sleep 2
done

#db check
while true
do
  java -jar /ops.jar -n /mysql.node -mysql exec >/tmp/db.log
  dbCnt=\`cat /tmp/db.log |grep -v select|grep cnt|awk -F ':' '{print \$NF}'\`
  echo "Current Table Count Status:\$dbCnt"
  if [ \$dbCnt -gt 100 ]; then
    echo "mysql table success!"
    break;
  fi
done

java -noverify -Dspring.config.location=/app/app/bpm/application.yml -Dfile.encoding=UTF-8 -Djava.io.tmpdir=/app/app/bpm/tmp -Dloader.path=/app/app/bpm/lib -jar /app/app/bpm/bpm.jar
EOF


if [[ -f ops.jar ]];then
  rm -rf ops.jar
fi
cp /app/app/bin/ops.jar.1 ops.jar

##生成Dockerfile文件
cat << EOF > Dockerfile
FROM registry.cn-hangzhou.aliyuncs.com/lank/jdk:s_1.8.342
MAINTAINER BPM
USER root
RUN mkdir -p         /app/app/bpm
RUN mkdir -p         /app/app/bpm/tmp
RUN mkdir -p         /app/app/bpm/bpm_logs
RUN mkdir -p         /app/app/bpm/lib
ADD ops.jar          /ops.jar
ADD bpm.jar          /app/app/bpm/bpm.jar
ADD $application_yml /app/app/bpm/application.yml
COPY entrypoint.sh   /entrypoint.sh
RUN chmod +x         /entrypoint.sh
ENTRYPOINT ["/entrypoint.sh"]
EOF

##build 生成image
docker build ./ -t registry.cn-hangzhou.aliyuncs.com/lank/app:$docker_image_flag

rm -rf application_tpl_docker.yml
rm -rf entrypoint.sh
rm -rf ops.jar
rm -rf Dockerfile
exit 0

#docker run -it -v /app/test:/app/app/app/upload  -d docker.io/openjdk:app10
