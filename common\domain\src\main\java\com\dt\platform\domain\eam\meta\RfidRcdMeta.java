package com.dt.platform.domain.eam.meta;

import com.github.foxnic.api.bean.BeanProperty;
import com.dt.platform.domain.eam.RfidRcd;
import java.util.Date;
import org.github.foxnic.web.domain.hrm.Employee;
import com.dt.platform.domain.eam.Asset;
import com.dt.platform.domain.eam.InventoryAsset;
import javax.persistence.Transient;



/**
 * <AUTHOR> , <EMAIL>
 * @since 2025-01-10 21:06:09
 * @sign E4B2CFD912E60E3A136A10408D93435E
 * 此文件由工具自动生成，请勿修改。若表结构或配置发生变动，请使用工具重新生成。
*/

public class RfidRcdMeta {
	
	/**
	 * 主键 , 类型: java.lang.String
	 */
	public static final String ID="id";
	
	/**
	 * 主键 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,java.lang.String> ID_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,ID, java.lang.String.class, "主键", "主键", java.lang.String.class, null);
	
	/**
	 * 业务类型 , 类型: java.lang.String
	 */
	public static final String BUSINESS_TYPE="businessType";
	
	/**
	 * 业务类型 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,java.lang.String> BUSINESS_TYPE_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,BUSINESS_TYPE, java.lang.String.class, "业务类型", "业务类型", java.lang.String.class, null);
	
	/**
	 * 单据 , 类型: java.lang.String
	 */
	public static final String BILL_ID="billId";
	
	/**
	 * 单据 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,java.lang.String> BILL_ID_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,BILL_ID, java.lang.String.class, "单据", "单据", java.lang.String.class, null);
	
	/**
	 * 资产 , 类型: java.lang.String
	 */
	public static final String ASSET_ID="assetId";
	
	/**
	 * 资产 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,java.lang.String> ASSET_ID_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,ASSET_ID, java.lang.String.class, "资产", "资产", java.lang.String.class, null);
	
	/**
	 * EPC , 类型: java.lang.String
	 */
	public static final String RFID_CODE="rfidCode";
	
	/**
	 * EPC , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,java.lang.String> RFID_CODE_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,RFID_CODE, java.lang.String.class, "EPC", "EPC", java.lang.String.class, null);
	
	/**
	 * 归属 , 类型: java.lang.String
	 */
	public static final String OWNER_ID="ownerId";
	
	/**
	 * 归属 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,java.lang.String> OWNER_ID_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,OWNER_ID, java.lang.String.class, "归属", "归属", java.lang.String.class, null);
	
	/**
	 * 状态 , 类型: java.lang.String
	 */
	public static final String STATUS="status";
	
	/**
	 * 状态 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,java.lang.String> STATUS_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,STATUS, java.lang.String.class, "状态", "状态", java.lang.String.class, null);
	
	/**
	 * RFID内容 , 类型: java.lang.String
	 */
	public static final String RFID_CONTENT="rfidContent";
	
	/**
	 * RFID内容 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,java.lang.String> RFID_CONTENT_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,RFID_CONTENT, java.lang.String.class, "RFID内容", "RFID内容", java.lang.String.class, null);
	
	/**
	 * 数量 , 类型: java.lang.Integer
	 */
	public static final String CNT="cnt";
	
	/**
	 * 数量 , 类型: java.lang.Integer
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,java.lang.Integer> CNT_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,CNT, java.lang.Integer.class, "数量", "数量", java.lang.Integer.class, null);
	
	/**
	 * 操作人员 , 类型: java.lang.String
	 */
	public static final String OPER_USER_ID="operUserId";
	
	/**
	 * 操作人员 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,java.lang.String> OPER_USER_ID_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,OPER_USER_ID, java.lang.String.class, "操作人员", "操作人员", java.lang.String.class, null);
	
	/**
	 * 操作时间 , 类型: java.util.Date
	 */
	public static final String RCD_TIME="rcdTime";
	
	/**
	 * 操作时间 , 类型: java.util.Date
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,java.util.Date> RCD_TIME_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,RCD_TIME, java.util.Date.class, "操作时间", "操作时间", java.util.Date.class, null);
	
	/**
	 * 备注 , 类型: java.lang.String
	 */
	public static final String NOTES="notes";
	
	/**
	 * 备注 , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,java.lang.String> NOTES_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,NOTES, java.lang.String.class, "备注", "备注", java.lang.String.class, null);
	
	/**
	 * 创建人ID , 类型: java.lang.String
	 */
	public static final String CREATE_BY="createBy";
	
	/**
	 * 创建人ID , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,java.lang.String> CREATE_BY_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,CREATE_BY, java.lang.String.class, "创建人ID", "创建人ID", java.lang.String.class, null);
	
	/**
	 * 创建时间 , 类型: java.util.Date
	 */
	public static final String CREATE_TIME="createTime";
	
	/**
	 * 创建时间 , 类型: java.util.Date
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,java.util.Date> CREATE_TIME_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,CREATE_TIME, java.util.Date.class, "创建时间", "创建时间", java.util.Date.class, null);
	
	/**
	 * 修改人ID , 类型: java.lang.String
	 */
	public static final String UPDATE_BY="updateBy";
	
	/**
	 * 修改人ID , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,java.lang.String> UPDATE_BY_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,UPDATE_BY, java.lang.String.class, "修改人ID", "修改人ID", java.lang.String.class, null);
	
	/**
	 * 修改时间 , 类型: java.util.Date
	 */
	public static final String UPDATE_TIME="updateTime";
	
	/**
	 * 修改时间 , 类型: java.util.Date
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,java.util.Date> UPDATE_TIME_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,UPDATE_TIME, java.util.Date.class, "修改时间", "修改时间", java.util.Date.class, null);
	
	/**
	 * 是否已删除 , 类型: java.lang.Integer
	 */
	public static final String DELETED="deleted";
	
	/**
	 * 是否已删除 , 类型: java.lang.Integer
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,java.lang.Integer> DELETED_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,DELETED, java.lang.Integer.class, "是否已删除", "是否已删除", java.lang.Integer.class, null);
	
	/**
	 * 删除人ID , 类型: java.lang.String
	 */
	public static final String DELETE_BY="deleteBy";
	
	/**
	 * 删除人ID , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,java.lang.String> DELETE_BY_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,DELETE_BY, java.lang.String.class, "删除人ID", "删除人ID", java.lang.String.class, null);
	
	/**
	 * 删除时间 , 类型: java.util.Date
	 */
	public static final String DELETE_TIME="deleteTime";
	
	/**
	 * 删除时间 , 类型: java.util.Date
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,java.util.Date> DELETE_TIME_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,DELETE_TIME, java.util.Date.class, "删除时间", "删除时间", java.util.Date.class, null);
	
	/**
	 * version , 类型: java.lang.Integer
	 */
	public static final String VERSION="version";
	
	/**
	 * version , 类型: java.lang.Integer
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,java.lang.Integer> VERSION_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,VERSION, java.lang.Integer.class, "version", "version", java.lang.Integer.class, null);
	
	/**
	 * operUser , 类型: org.github.foxnic.web.domain.hrm.Employee
	 */
	public static final String OPER_USER="operUser";
	
	/**
	 * operUser , 类型: org.github.foxnic.web.domain.hrm.Employee
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,org.github.foxnic.web.domain.hrm.Employee> OPER_USER_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,OPER_USER, org.github.foxnic.web.domain.hrm.Employee.class, "operUser", "operUser", org.github.foxnic.web.domain.hrm.Employee.class, null);
	
	/**
	 * asset , 类型: com.dt.platform.domain.eam.Asset
	 */
	public static final String ASSET="asset";
	
	/**
	 * asset , 类型: com.dt.platform.domain.eam.Asset
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,com.dt.platform.domain.eam.Asset> ASSET_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,ASSET, com.dt.platform.domain.eam.Asset.class, "asset", "asset", com.dt.platform.domain.eam.Asset.class, null);
	
	/**
	 * inventoryAsset , 类型: com.dt.platform.domain.eam.InventoryAsset
	 */
	public static final String INVENTORY_ASSET="inventoryAsset";
	
	/**
	 * inventoryAsset , 类型: com.dt.platform.domain.eam.InventoryAsset
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,com.dt.platform.domain.eam.InventoryAsset> INVENTORY_ASSET_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,INVENTORY_ASSET, com.dt.platform.domain.eam.InventoryAsset.class, "inventoryAsset", "inventoryAsset", com.dt.platform.domain.eam.InventoryAsset.class, null);
	
	/**
	 * queryType , 类型: java.lang.String
	 */
	public static final String QUERY_TYPE="queryType";
	
	/**
	 * queryType , 类型: java.lang.String
	 */
	public static final BeanProperty<com.dt.platform.domain.eam.RfidRcd,java.lang.String> QUERY_TYPE_PROP = new BeanProperty(com.dt.platform.domain.eam.RfidRcd.class ,QUERY_TYPE, java.lang.String.class, "queryType", "queryType", java.lang.String.class, null);
	
	/**
	 * 全部属性清单
	 */
	public static final String[] $PROPS={ ID , BUSINESS_TYPE , BILL_ID , ASSET_ID , RFID_CODE , OWNER_ID , STATUS , RFID_CONTENT , CNT , OPER_USER_ID , RCD_TIME , NOTES , CREATE_BY , CREATE_TIME , UPDATE_BY , UPDATE_TIME , DELETED , DELETE_BY , DELETE_TIME , VERSION , OPER_USER , ASSET , INVENTORY_ASSET , QUERY_TYPE };
	
	/**
	 * 代理类
	 */
	public static class $$proxy$$ extends com.dt.platform.domain.eam.RfidRcd {

		private static final long serialVersionUID = 1L;

		
		/**
		 * 设置 主键
		 * @param id 主键
		 * @return 当前对象
		*/
		public RfidRcd setId(String id) {
			super.change(ID,super.getId(),id);
			super.setId(id);
			return this;
		}
		
		/**
		 * 设置 业务类型
		 * @param businessType 业务类型
		 * @return 当前对象
		*/
		public RfidRcd setBusinessType(String businessType) {
			super.change(BUSINESS_TYPE,super.getBusinessType(),businessType);
			super.setBusinessType(businessType);
			return this;
		}
		
		/**
		 * 设置 单据
		 * @param billId 单据
		 * @return 当前对象
		*/
		public RfidRcd setBillId(String billId) {
			super.change(BILL_ID,super.getBillId(),billId);
			super.setBillId(billId);
			return this;
		}
		
		/**
		 * 设置 资产
		 * @param assetId 资产
		 * @return 当前对象
		*/
		public RfidRcd setAssetId(String assetId) {
			super.change(ASSET_ID,super.getAssetId(),assetId);
			super.setAssetId(assetId);
			return this;
		}
		
		/**
		 * 设置 EPC
		 * @param rfidCode EPC
		 * @return 当前对象
		*/
		public RfidRcd setRfidCode(String rfidCode) {
			super.change(RFID_CODE,super.getRfidCode(),rfidCode);
			super.setRfidCode(rfidCode);
			return this;
		}
		
		/**
		 * 设置 归属
		 * @param ownerId 归属
		 * @return 当前对象
		*/
		public RfidRcd setOwnerId(String ownerId) {
			super.change(OWNER_ID,super.getOwnerId(),ownerId);
			super.setOwnerId(ownerId);
			return this;
		}
		
		/**
		 * 设置 状态
		 * @param status 状态
		 * @return 当前对象
		*/
		public RfidRcd setStatus(String status) {
			super.change(STATUS,super.getStatus(),status);
			super.setStatus(status);
			return this;
		}
		
		/**
		 * 设置 RFID内容
		 * @param rfidContent RFID内容
		 * @return 当前对象
		*/
		public RfidRcd setRfidContent(String rfidContent) {
			super.change(RFID_CONTENT,super.getRfidContent(),rfidContent);
			super.setRfidContent(rfidContent);
			return this;
		}
		
		/**
		 * 设置 数量
		 * @param cnt 数量
		 * @return 当前对象
		*/
		public RfidRcd setCnt(Integer cnt) {
			super.change(CNT,super.getCnt(),cnt);
			super.setCnt(cnt);
			return this;
		}
		
		/**
		 * 设置 操作人员
		 * @param operUserId 操作人员
		 * @return 当前对象
		*/
		public RfidRcd setOperUserId(String operUserId) {
			super.change(OPER_USER_ID,super.getOperUserId(),operUserId);
			super.setOperUserId(operUserId);
			return this;
		}
		
		/**
		 * 设置 操作时间
		 * @param rcdTime 操作时间
		 * @return 当前对象
		*/
		public RfidRcd setRcdTime(Date rcdTime) {
			super.change(RCD_TIME,super.getRcdTime(),rcdTime);
			super.setRcdTime(rcdTime);
			return this;
		}
		
		/**
		 * 设置 备注
		 * @param notes 备注
		 * @return 当前对象
		*/
		public RfidRcd setNotes(String notes) {
			super.change(NOTES,super.getNotes(),notes);
			super.setNotes(notes);
			return this;
		}
		
		/**
		 * 设置 创建人ID
		 * @param createBy 创建人ID
		 * @return 当前对象
		*/
		public RfidRcd setCreateBy(String createBy) {
			super.change(CREATE_BY,super.getCreateBy(),createBy);
			super.setCreateBy(createBy);
			return this;
		}
		
		/**
		 * 设置 创建时间
		 * @param createTime 创建时间
		 * @return 当前对象
		*/
		public RfidRcd setCreateTime(Date createTime) {
			super.change(CREATE_TIME,super.getCreateTime(),createTime);
			super.setCreateTime(createTime);
			return this;
		}
		
		/**
		 * 设置 修改人ID
		 * @param updateBy 修改人ID
		 * @return 当前对象
		*/
		public RfidRcd setUpdateBy(String updateBy) {
			super.change(UPDATE_BY,super.getUpdateBy(),updateBy);
			super.setUpdateBy(updateBy);
			return this;
		}
		
		/**
		 * 设置 修改时间
		 * @param updateTime 修改时间
		 * @return 当前对象
		*/
		public RfidRcd setUpdateTime(Date updateTime) {
			super.change(UPDATE_TIME,super.getUpdateTime(),updateTime);
			super.setUpdateTime(updateTime);
			return this;
		}
		
		/**
		 * 设置 是否已删除
		 * @param deleted 是否已删除
		 * @return 当前对象
		*/
		public RfidRcd setDeleted(Integer deleted) {
			super.change(DELETED,super.getDeleted(),deleted);
			super.setDeleted(deleted);
			return this;
		}
		
		/**
		 * 设置 删除人ID
		 * @param deleteBy 删除人ID
		 * @return 当前对象
		*/
		public RfidRcd setDeleteBy(String deleteBy) {
			super.change(DELETE_BY,super.getDeleteBy(),deleteBy);
			super.setDeleteBy(deleteBy);
			return this;
		}
		
		/**
		 * 设置 删除时间
		 * @param deleteTime 删除时间
		 * @return 当前对象
		*/
		public RfidRcd setDeleteTime(Date deleteTime) {
			super.change(DELETE_TIME,super.getDeleteTime(),deleteTime);
			super.setDeleteTime(deleteTime);
			return this;
		}
		
		/**
		 * 设置 version
		 * @param version version
		 * @return 当前对象
		*/
		public RfidRcd setVersion(Integer version) {
			super.change(VERSION,super.getVersion(),version);
			super.setVersion(version);
			return this;
		}
		
		/**
		 * 设置 operUser
		 * @param operUser operUser
		 * @return 当前对象
		*/
		public RfidRcd setOperUser(Employee operUser) {
			super.change(OPER_USER,super.getOperUser(),operUser);
			super.setOperUser(operUser);
			return this;
		}
		
		/**
		 * 设置 asset
		 * @param asset asset
		 * @return 当前对象
		*/
		public RfidRcd setAsset(Asset asset) {
			super.change(ASSET,super.getAsset(),asset);
			super.setAsset(asset);
			return this;
		}
		
		/**
		 * 设置 inventoryAsset
		 * @param inventoryAsset inventoryAsset
		 * @return 当前对象
		*/
		public RfidRcd setInventoryAsset(InventoryAsset inventoryAsset) {
			super.change(INVENTORY_ASSET,super.getInventoryAsset(),inventoryAsset);
			super.setInventoryAsset(inventoryAsset);
			return this;
		}
		
		/**
		 * 设置 queryType
		 * @param queryType queryType
		 * @return 当前对象
		*/
		public RfidRcd setQueryType(String queryType) {
			super.change(QUERY_TYPE,super.getQueryType(),queryType);
			super.setQueryType(queryType);
			return this;
		}

		/**
		 * 克隆当前对象
		*/
		@Transient
		public RfidRcd clone() {
			return duplicate(true);
		}

		/**
		 * 复制当前对象
		 * @param all 是否复制全部属性，当 false 时，仅复制来自数据表的属性
		*/
		@Transient
		public RfidRcd duplicate(boolean all) {
			$$proxy$$ inst=new $$proxy$$();
			inst.setOperUserId(this.getOperUserId());
			inst.setNotes(this.getNotes());
			inst.setCnt(this.getCnt());
			inst.setUpdateTime(this.getUpdateTime());
			inst.setOwnerId(this.getOwnerId());
			inst.setVersion(this.getVersion());
			inst.setCreateBy(this.getCreateBy());
			inst.setRfidCode(this.getRfidCode());
			inst.setDeleted(this.getDeleted());
			inst.setCreateTime(this.getCreateTime());
			inst.setUpdateBy(this.getUpdateBy());
			inst.setDeleteTime(this.getDeleteTime());
			inst.setAssetId(this.getAssetId());
			inst.setBillId(this.getBillId());
			inst.setDeleteBy(this.getDeleteBy());
			inst.setId(this.getId());
			inst.setRfidContent(this.getRfidContent());
			inst.setBusinessType(this.getBusinessType());
			inst.setStatus(this.getStatus());
			inst.setRcdTime(this.getRcdTime());
			if(all) {
				inst.setInventoryAsset(this.getInventoryAsset());
				inst.setAsset(this.getAsset());
				inst.setOperUser(this.getOperUser());
				inst.setQueryType(this.getQueryType());
			}
			inst.clearModifies();
			return inst;
		}

	}
}