#!/bin/sh
#################################################################################
# /app/app/tengine目录下
#
#################################################################################
cur_dir=$(cd `dirname $0`; pwd)
cd $cur_dir
echo "cur_dir:$cur_dir"
docker_image_version=2.9.2
if [[ -n $1 ]];then
  docker_image_version=$1
fi
docker_image_flag=fapp_nginx_${docker_image_version}
echo "docker image:$docker_image_flag"
app_ip=***********
app_port=8089
app_api=http://${app_ip}:${app_port}
v1="资产管理社区免费版"
v2="资产管理社区免费版"
cd $cur_dir
##生成Dockerfile文件
cat << EOF > nginx_docker.conf
worker_processes auto;
worker_rlimit_nofile 65535;
events {
    use epoll;
    multi_accept on;
    worker_connections 1024;
}
http {
    include       mime.types;
    default_type  application/octet-stream;
    log_format  main  '\$remote_addr - \$remote_user [\$time_local] "\$request" '
                  '\$status \$body_bytes_sent "\$http_referer" '
                  '"\$http_user_agent" "\$http_x_forwarded_for"';
    access_log  /var/log/nginx/access.log main;
    error_log   /var/log/nginx/error.log warn;

## 基本设置
#pagespeed on;
#pagespeed FileCachePath /var/ngx_pagespeed_cache;
## 禁用CoreFilters
#pagespeed RewriteLevel PassThrough;
## 启用必要的优化过滤器
#pagespeed EnableFilters collapse_whitespace;
#pagespeed EnableFilters canonicalize_javascript_libraries;
#pagespeed EnableFilters combine_css;
##pagespeed EnableFilters combine_javascript;
#pagespeed EnableFilters elide_attributes;
#pagespeed EnableFilters extend_cache;
#pagespeed EnableFilters flatten_css_imports;
#pagespeed CssFlattenMaxBytes 5120;
#pagespeed EnableFilters lazyload_images;
##pagespeed EnableFilters rewrite_javascript;
#pagespeed EnableFilters insert_dns_prefetch;
#pagespeed EnableFilters prioritize_critical_css;
## 禁止对特定目录的优化（可选）
#pagespeed Disallow "*/system/*";
## 图片处理配置
#pagespeed EnableFilters lazyload_images;
## 不加载显示区域以外的图片
#pagespeed LazyloadImagesAfterOnload off;
##pagespeed LazyloadImagesBlankUrl "https://www.xxx.com/xxx.png";
## 启用图片优化机制(主要是 inline_images, recompress_images, convert_to_webp_lossless（这个命令会把PNG和静态Gif图片转化为webp）, and resize_images.)
#pagespeed EnableFilters rewrite_images;
##组合 convert_gif_to_png, convert_jpeg_to_progressive, convert_jpeg_to_webp, convert_png_to_jpeg, jpeg_subsampling, recompress_jpeg, recompress_png, recompress_webp, #strip_image_color_profile, and strip_image_meta_data.
#pagespeed EnableFilters recompress_images;
## 将JPEG图片转化为webp格式
#pagespeed EnableFilters convert_jpeg_to_webp;
## 将动画Gif图片转化为动画webp格式
#pagespeed EnableFilters convert_to_webp_animated;
## 图片预加载
#pagespeed EnableFilters inline_preview_images;
## 让JS里引用的图片也加入优化
#pagespeed InPlaceResourceOptimization on;
#pagespeed EnableFilters in_place_optimize_for_browser;
    proxy_buffering on;
    proxy_buffer_size 8k;
    proxy_buffers 4 32k;
    proxy_busy_buffers_size 64K;
    proxy_temp_path /var/nginxtemp 1 2;
    proxy_max_temp_file_size 20m;
    proxy_temp_file_write_size 256k;
    #cache
    proxy_cache_path /var/nginxcache levels=1:1 keys_zone=cache_one:200m inactive=3d max_size=30g;
    server_tokens off;
    keepalive_timeout 60;
    keepalive_requests 1024;
    client_header_buffer_size 512k;
    client_body_buffer_size 1m;
    client_max_body_size 256m;
    sendfile on;
    #big file
    tcp_nopush on;
    #small file
    tcp_nodelay on;
    open_file_cache max=65535 inactive=60s;
    open_file_cache_valid 60s;
    open_file_cache_min_uses 1;
    open_file_cache_errors on;
    gzip on;
    gzip_static on;
    gzip_min_length 1k;
    gzip_comp_level 9;
    gzip_buffers 16 8k;
    gzip_types image/jpeg image/gif image/x-icon image/png application/font-woff2 text/plain text/css application/x-font-ttf application/json application/x-javascript application/javascript text/javascript;
    gzip_vary on;
    gzip_proxied any;
    gzip_disable "MSIE [1-6]\\.";
    gzip_http_version 1.1;
    add_header Access-Control-Allow-Origin *;
    server {
        listen *:8899 default_server;
        server_name resource.xyjm.store;
        location /ngx_status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            deny all;
          }
    }
    server {
        listen 8088;
        server_name resource.xyjm.store;
        keepalive_timeout 60;
        location / {
          proxy_pass $app_api;
          proxy_redirect off;
          proxy_set_header Host \$host:\$server_port;
          proxy_set_header X-Real-IP \$remote_addr;
          proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        }
        location = /login.js {
          gzip off;
          gzip_static off;
          proxy_pass $app_api;
          proxy_redirect off;
          proxy_set_header Host \$host:\$server_port;
          proxy_set_header X-Real-IP \$remote_addr;
          add_header M 'MM';
          proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
          sub_filter_once off;
          sub_filter_types *;
        }
        location = /login.html {
          gzip off;
          gzip_static off;
          proxy_pass $app_api;
          proxy_redirect off;
          proxy_set_header Host \$host:\$server_port;
          proxy_set_header X-Real-IP \$remote_addr;
          add_header M 'MM';
          proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
          sub_filter 'login' 'login';
          sub_filter '$v1' '$v2';
          sub_filter_once off;
          sub_filter_types *;
        }
         location = /module/foxnic.js {
          gzip off;
          gzip_static off;
          proxy_pass $app_api;
          proxy_redirect off;
          proxy_set_header Host \$host:\$server_port;
          proxy_set_header X-Real-IP \$remote_addr;
          add_header M 'MM';
          proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
          sub_filter 'login' 'login';
          sub_filter '$v1' '$v2';
          sub_filter_once off;
          sub_filter_types *;
        }
        location ~* ^/(asset|business/common)/.+\.(jpg|jpeg|png|gif|css|js)$ {
          access_log off;
          add_header Cache-Control public;
          proxy_pass $app_api;
          proxy_redirect off;
          proxy_set_header Host \$host:\$server_port;
          proxy_set_header X-Real-IP \$remote_addr;
          proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
          add_header X-Cache-Status \$upstream_cache_status;
          proxy_cache_key \$host\$uri\$is_args\$args;
          proxy_cache cache_one;
          proxy_cache_valid 200 7d;
          proxy_cache_valid 302 1h;
          proxy_cache_valid 301 1d;
          proxy_cache_valid any 1m;
          expires 30d;
        }
        location /business/common/page_info/ {
          proxy_pass $app_api;
          proxy_redirect off;
          add_header Cache-Control public;
          proxy_set_header Host \$host:\$server_port;
          proxy_set_header X-Real-IP \$remote_addr;
          proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
          add_header X-Cache-Status \$upstream_cache_status;
          proxy_cache_key \$host\$uri\$is_args\$args;
          proxy_cache cache_one;
          proxy_cache_valid 200 7d;
          proxy_cache_valid 302 1h;
          proxy_cache_valid 301 1d;
          proxy_cache_valid any 1m;
          expires 30d;
         }
    }
    server {
        listen 8090 ssl http2;
        server_name resource.xyjm.store;
        keepalive_timeout 60;
        ssl_certificate /nginx/cert/resource.xyjm.store.pem;
        ssl_certificate_key /nginx/cert/resource.xyjm.store.key;
        ssl_session_timeout 5m;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
        location / {
          proxy_http_version 1.1;
          proxy_pass $app_api;
          proxy_redirect http:// https://;
          proxy_set_header Host \$host:\$server_port;
          proxy_set_header X-Real-IP \$remote_addr;
          proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        }
      location ~* ^/(asset|business/common)/.+\.(jpg|jpeg|png|gif|css|js)$ {
          proxy_http_version 1.1;
          access_log off;
          add_header Cache-Control public;
          proxy_pass $app_api;
          proxy_redirect http:// https://;
          proxy_set_header Host \$host:\$server_port;
          proxy_set_header X-Real-IP \$remote_addr;
          proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
          proxy_cache_key \$host\$uri\$is_args\$args;
          add_header X-Cache-Status \$upstream_cache_status;
          proxy_cache cache_one;
          proxy_cache_valid 200 7d;
          proxy_cache_valid 302 1h;
          proxy_cache_valid 301 1d;
          proxy_cache_valid any 1m;
          expires 30d;
        }
        location = /console/pear/component/pear/css/pear-support.css {
          proxy_http_version 1.1;
          gzip off;
          gzip_static off;
          proxy_pass $app_api;
          proxy_redirect http:// https://;
          proxy_redirect http://$app_ip:8089 https://$app_ip:8090;
          proxy_set_header Host \$host:\$server_port;
          proxy_set_header X-Real-IP \$remote_addr;
          proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        }
        location = /login.js {
          proxy_http_version 1.1;
          gzip off;
          gzip_static off;
          proxy_pass $app_api;
          proxy_redirect http:// https://;
          proxy_set_header Host \$host:\$server_port;
          proxy_set_header X-Real-IP \$remote_addr;
          add_header M 'MM';
          proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
          sub_filter_once off;
          sub_filter_types *;
        }
        location = /login.html {
          proxy_http_version 1.1;
          gzip off;
          gzip_static off;
          proxy_pass $app_api;
          proxy_redirect http:// https://;
          proxy_set_header Host \$host:\$server_port;
          proxy_set_header X-Real-IP \$remote_addr;
          add_header M 'MM';
          proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
          sub_filter 'login' 'login';
          sub_filter '$v1' '$v2';
          sub_filter_once off;
          sub_filter_types *;
        }
         location = /module/foxnic.js {
          proxy_http_version 1.1;
          gzip off;
          gzip_static off;
          proxy_pass $app_api;
          proxy_redirect http:// https://;
          proxy_set_header Host \$host:\$server_port;
          proxy_set_header X-Real-IP \$remote_addr;
          add_header M 'MM';
          proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
          sub_filter 'login' 'login';
          sub_filter '$v1' '$v2';
          sub_filter_once off;
          sub_filter_types *;
        }
        location /business/common/page_info/ {
          proxy_http_version 1.1;
          proxy_pass $app_api;
          proxy_redirect http:// https://;
          add_header X-Cache-Status \$upstream_cache_status;
          proxy_set_header Host \$host:\$server_port;
          proxy_set_header X-Real-IP \$remote_addr;
          proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
          add_header X-Cache-Status \$upstream_cache_status;
          proxy_cache_key \$host\$uri\$is_args\$args;
          proxy_cache cache_one;
          proxy_cache_valid 200 7d;
          proxy_cache_valid 302 1h;
          proxy_cache_valid 301 1d;
          proxy_cache_valid any 1m;
          expires 30d;
         }
    }
    server {
        listen 8091 ssl;
        server_name resource.xyjm.store;
        root  /nginx/html;
        index index.html index.htm;
        ssl_certificate /nginx/cert/resource.xyjm.store.pem;
        ssl_certificate_key /nginx/cert/resource.xyjm.store.key;
        ssl_session_timeout 5m;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
        ssl_prefer_server_ciphers on;
        location / {
            index index.html index.htm;
            proxy_redirect http:// https://;
            proxy_set_header Host \$host:\$server_port;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
         }
          location /service  {
            proxy_redirect http:// https://;
            proxy_set_header Host \$host:\$server_port;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_pass $app_api;
        }
        location /business/common/page_info/{
            proxy_http_version 1.1;
            proxy_pass $app_api;
            proxy_redirect http:// https://;
            add_header X-Cache-Status \$upstream_cache_status;
            proxy_set_header Host \$host:\$server_port;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            add_header X-Cache-Status \$upstream_cache_status;
            proxy_cache_key \$host\$uri\$is_args\$args;
            proxy_cache cache_one;
            proxy_cache_valid 200 7d;
            proxy_cache_valid 302 1h;
            proxy_cache_valid 301 1d;
            proxy_cache_valid any 1m;
            expires 30d;
        }
        location /security {
            proxy_redirect http:// https://;
            proxy_set_header Host \$host:\$server_port;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_pass $app_api;
        }
    }
}
EOF

##生成Dockerfile文件
if [[ -f "resource.xyjm.store.pem" ]];then
  rm -rf resource.xyjm.store.pem
fi
if [[ -f "resource.xyjm.store.key" ]];then
  rm -rf resource.xyjm.store.key
fi

#cert auto create
if [[ -f "/app/app/tengine/cert/resource.xyjm.store.pem" ]];then
  rm -rf /app/app/tengine/cert/resource.xyjm.store.pem
fi
if [[ -f "/app/app/tengine/cert/resource.xyjm.store.key" ]];then
  rm -rf /app/app/tengine/cert/resource.xyjm.store.key
fi
cp /root/.acme.sh/resource.xyjm.store/resource.xyjm.store.cer /app/app/tengine/cert/resource.xyjm.store.pem
cp /root/.acme.sh/resource.xyjm.store/resource.xyjm.store.key /app/app/tengine/cert/resource.xyjm.store.key
cp cert/resource.xyjm.store.pem resource.xyjm.store.pem
cp cert/resource.xyjm.store.key resource.xyjm.store.key
rm -rf nginx
cp /usr/sbin/nginx .
chmod 755 nginx
if [[ -d "html" ]];then
  rm -rf html
fi
mkdir html
cp h5.tar.gz.1 html
cd html
tar xvf h5.tar.gz.1
cd $cur_dir




cat << EOF > Dockerfile
FROM registry.cn-hangzhou.aliyuncs.com/lank/nginx:s_1.21.5_f
MAINTAINER NGINX
RUN mkdir -p              /nginx/cert
RUN mkdir -p              /nginx/html
RUN mkdir -p              /var/log/nginx
COPY nginx_docker.conf    /etc/nginx/nginx.conf
ADD resource.xyjm.store.pem   /nginx/cert
ADD resource.xyjm.store.key   /nginx/cert
ADD html                      /nginx/html
EOF
##build 生成image
docker build ./ -t registry.cn-hangzhou.aliyuncs.com/lank/nginx:$docker_image_flag
exit 0



