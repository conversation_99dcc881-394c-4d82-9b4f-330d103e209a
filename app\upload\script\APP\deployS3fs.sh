#!/bin/sh
#######################################################################
#     将网盘和应用数据挂载到文件系统上
#
#######################################################################
url=http://127.0.0.1:30003
accessKey=iLD9PcLSqv5Q0ELXpXQN
secretKey=nTdOWm5rW54ZpyZ6iCJovEedOwUq5uXe9W6H4kHW

echo "$accessKey:$secretKey" > /etc/passwd-s3fs
chmod 600 /etc/passwd-s3fs
mkdir -p /app/s3fsnetdisk
mkdir -p /app/s3fsdata

s3fs appnetdisk /app/s3fsnetdisk -o passwd_file=/etc/passwd-s3fs -o url=$url \
-o umask=000 \
-o no_check_certificate \
-o allow_other \
-o use_path_request_style \
-o use_cache=/dev/shm \
-o kernel_cache \
-o max_background=1000 \
-o max_stat_cache_size=100000 \
-o multipart_size=64 \
-o parallel_count=30 \
-o multireq_max=30 \
-o dbglevel=warn

s3fs appdata /app/s3fsdata -o passwd_file=/etc/passwd-s3fs -o url=$url \
-o umask=000 \
-o no_check_certificate \
-o allow_other \
-o use_path_request_style \
-o use_cache=/dev/shm \
-o kernel_cache \
-o max_background=1000 \
-o max_stat_cache_size=100000 \
-o multipart_size=64 \
-o parallel_count=30 \
-o multireq_max=30 \
-o dbglevel=warn

exit 0
