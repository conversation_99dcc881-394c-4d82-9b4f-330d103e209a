#!/bin/sh
#
#替换jar中BOOT-INF/lib的jar包
################################################
app_file=""
jar_files=""
cur_dir=$(cd `dirname $0`; pwd)
uid=`date +%Y%m%d%H%M%S`
if [[ -n $1 ]];then
  app_file=$1
fi
if [[ -n $2 ]];then
  jar_files=$2
fi
echo "app_file:$app_file"
echo "jar_files:$jar_files"
tmpDir=/tmp/$$/${uid}
if [[ ! -f $app_file ]];then
  echo "appFile,$appfile not exist"
  exit 1
else
  mkdir -p $tmpDir
  cp $app_file ${app_file}_${uid}
  cp $app_file $tmpDir
fi
cd $tmpDir
unzip $app_file
rm -rf $app_file

IFS=',' read -ra items <<< "$jar_files"

for jar_file in "${items[@]}"; do
 jar_file_s=$tmpDir/BOOT-INF/lib/$jar_file
  echo "start to process $jar_file_s"
  if [[ -f $jar_file_s ]];then
     rm -rf $jar_file_s
  else
    echo "$jar_file_s not exist"
  fi
  echo "copy $cur_dir/$jar_file $tmpDir/BOOT-INF/lib/"
  cp $jar_file $tmpDir/BOOT-INF/lib/
  echo "process success"
  echo ""
done
echo "start to build jar"
jar -cvfm0 $app_file META-INF/MANIFEST.MF ./
rm -rf $cur_dir/$app_file
cp $app_file $cur_dir/
exit 0