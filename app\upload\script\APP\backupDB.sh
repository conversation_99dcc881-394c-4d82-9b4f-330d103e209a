#!/bin/sh
#cron:
#   30 2 * * * sh /app/data/backupDB.sh app
#
########################################
cur_dir=$(cd `dirname $0`; pwd)
app_dir=$cur_dir
backup_dir=$app_dir/backup/db
MYSQL=mysql
MYSQL_DUMP=mysqldump
DB_NAME=app
DB_HOST=127.0.0.1
DB_PORT=33306
DB_USER=root
DB_PWD=P@ssw0rd123456
TODAY=`date +%Y%m%d_%H_%M_%S`

if [[ ! -d $backup_dir ]];then
  mkdir -p $backup_dir
fi
if [[ ! -d $backup_dir ]];then
  echo "backup dir $backup_dir not exist";
  exit 1
fi

dbname=`echo $DB_NAME`
if [ $1 ];then
  echo "set dbname:$1"
  dbname=$1
fi
file=${dbname}_backup_$TODAY.tar.gz
echo "start to backup,dbname:$dbname,file:$backup_dir/$file"
cd $backup_dir
$MYSQL_DUMP -R -P$DB_PORT -u$DB_USER -p$DB_PWD -h$DB_HOST $dbname > db.sql
tar zcvf $file  ./db.sql
echo "backup finish"
echo "$backup_dir/db.sql"

$app_dir
exit 0

