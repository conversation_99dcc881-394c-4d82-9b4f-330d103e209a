<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dt.platform</groupId>
        <artifactId>parent-eam</artifactId>
        <version>*******</version>
        <relativePath>../../pom.xml</relativePath>
        <!-- lookup parent from repository -->
    </parent>
    <artifactId>domain-eam</artifactId>
    <name>domain-platform-eam</name>
    <description>domain-platform</description>
    <packaging>jar</packaging>
    <dependencies>
        <dependency>
            <groupId>com.foxnicweb</groupId>
            <artifactId>foxnic-sql</artifactId>
            <version>${foxnic.version}</version>
        </dependency>
        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>domain</artifactId>
            <version>${foxnic-web.version}</version>
        </dependency>


        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>contract-domain</artifactId>
            <version>${foxnic-contract.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>domain-oa</artifactId>
            <version>${foxnic-oa.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>domain-ops</artifactId>
            <version>${foxnic-ops.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>domain-hr</artifactId>
            <version>${foxnic-hr.version}</version>
        </dependency>


        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>customer-domain</artifactId>
            <version>${foxnic-customer.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-micro-spring-boot-starter</artifactId>
            <version>${knife4j.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>domain-web</artifactId>
            <version>${foxnic-web-plus.version}</version>
        </dependency>

        <!-- 添加fastjson依赖 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>

    </dependencies>
</project>
