#!/bin/sh
#################################################################################
# /app/app/tengine目录下
#
#################################################################################
cur_dir=$(cd `dirname $0`; pwd)
cd $cur_dir

docker_image_version=1.0.1
if [[ -n $1 ]];then
  docker_image_version=$1
fi
docker_image_flag=fwebsite_nginx_${docker_image_version}
echo "docker image:$docker_image_flag"

##生成Dockerfile文件
cat << EOF > nginx_docker.conf
worker_processes  2;
events {
    worker_connections  1024;
}
http {
    include       mime.types;
    default_type  application/octet-stream;
    log_format  main  '\$remote_addr - \$remote_user [\$time_local] "\$request" '
                  '\$status \$body_bytes_sent "\$http_referer" '
                  '"\$http_user_agent" "\$http_x_forwarded_for"';
    access_log  /var/log/nginx/access.log main;
    error_log   /var/log/nginx/error.log warn;
    client_max_body_size 50m;
    sendfile        on;
    keepalive_timeout  65;
    server {
        listen       12345;
        server_name  localhost;
        location / {
            root   html;
            index  index.html index.htm;
        }
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
    server {
        #nginx status
        listen *:8899 default_server;
        server_name app.com;
        location /ngx_status {
            stub_status on;
            access_log on;
            #allow 127.0.0.1;
            #deny all;
          }
    }
    server {
        listen 8686;
        server_name app.com;
        root  /nginx/html;
        index index.html index.htm;
        location / {
            index index.html index.htm;
            proxy_set_header HOST \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-FOR \$proxy_add_x_forwarded_for;
         }
    }
}
EOF
##生成Dockerfile文件
cat << EOF > Dockerfile
FROM registry.cn-hangzhou.aliyuncs.com/lank/nginx:s_1.21.5
MAINTAINER NGINX
RUN mkdir -p              /nginx/cert
RUN mkdir -p              /nginx/html
RUN mkdir -p              /var/log/nginx
COPY nginx_docker.conf    /etc/nginx/nginx.conf
ADD html                  /nginx/html
EOF
##build 生成image
docker build ./ -t registry.cn-hangzhou.aliyuncs.com/lank/nginx:$docker_image_flag
##clear
cd $cur_dir
rm -rf Dockerfile
rm -rf nginx_docker.conf
exit 0





