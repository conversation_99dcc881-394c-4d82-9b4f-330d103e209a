<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title></title>
    <script
            src="http://shopcdn.maimaiyouhuiquan.com/shop/console/vendor/jquery/dist/jquery.min.js"></script>
    <style type="text/css">
        body, ol, ul, li, h1, h2, h3, h4, h5, h6, p, th, td, dl, dd, form,
        fieldset, legend, input, textarea, select {
            margin: 0;
            padding: 0
        }

        img {
            border: 0;
            vertical-align: top
        }

        li {
            list-style: none
        }

        iframe {
            border: none
        }

        html {
            height: 100%;
            font-size: 16px
        }

        body {
            height: 100%;
            font: 400 1em/1.8 'Microsoft Yahei', 'PingFang SC', 'Avenir', 'Segoe UI',
            'Hiragino Sans GB', STHeiti, 'Microsoft Sans Serif',
            'WenQuanYi Micro Hei', sans-serif
        }

        a {
            color: #333;
            text-decoration: none
        }

        a:hover {
            color: #0c82ff
        }

        .cf:after, .cf:before {
            content: " ";
            display: table
        }

        .cf:after, .clearfix:after {
            clear: both
        }

        .fl {
            float: left
        }

        .fr {
            float: right
        }

        h1, h2, h3, h4, h5 {
            font-size: 100%;
            font-weight: normal
        }

        .f12 {
            font-size: 12px
        }

        .f14 {
            font-size: 14px
        }

        .f16 {
            font-size: 16px
        }

        .f18 {
            font-size: 18px
        }

        .f20 {
            font-size: 20px
        }

        .bold {
            font-weight: bold
        }

        .l16 {
            line-height: 16px
        }

        .l18 {
            line-height: 18px
        }

        .l20 {
            line-height: 20px
        }

        .l22 {
            line-height: 22px
        }

        .l24 {
            line-height: 24px
        }

        .l26 {
            line-height: 26px
        }

        .l28 {
            line-height: 28px
        }

        .l30 {
            line-height: 30px
        }

        .dis {
            display: block
        }

        .undis {
            display: none
        }

        .layout {
            width: 1400px;
            margin: 0 auto
        }

        .layout .news-top-first {
            height: 36px
        }

        .qq-footer {
            font-size: 14px;
            text-align: center;
            color: #666666;
            line-height: 30px;
            margin-bottom: 0
        }

        .qq-footer a {
            color: #666666
        }

        .qq-footer a:hover {
            color: #0c82ff
        }

        .qq-footer .footernew {
            color: #515151;
            font-size: 12px;
            height: 70px;
            line-height: 24px;
            margin: 12px auto 0;
            overflow: hidden;
            text-align: center;
            width: 1000px
        }

        .qq-footer .footernew a {
            color: #515151;
            text-decoration: none
        }

        .qq-footer .footernew a:hover {
            color: #0c82ff
        }

        .qq-footer .footernew .footernewdiv {
            margin: 0 auto;
            width: 680px
        }

        .qq-footer .footernew p {
            border: 1px solid #D2D2D2;
            float: left;
            font-size: 12px;
            height: 50px;
            line-height: 20px;
            margin: 6px;
            padding: 0;
            width: 120px;
            overflow: hidden
        }

        .qq-footer .footernew .fl {
            padding: 3px
        }

        .qq-footer .footernew .fr {
            padding: 5px 3px 0;
            width: 72px
        }

        .footer {
            position: absolute;
            bottom: 0;
            width: 100%;
            height: 60x;
            background-color: #f0ad4e;
        }

        .ct {
            position: absolute;
            top: 0;
            width: 100%;
            height: 60x;
        }

        .ctv {
            margin-top: 120px;
            padding: 0;
            text-align: center;
        }
    </style>
</head>
<body>
<div class="ct">
    <div class="ctv">
        <span>Hello!</span>
    </div>
</div>
</body>
<script>
    window.location.href="views/index.html"
</script>
</html>