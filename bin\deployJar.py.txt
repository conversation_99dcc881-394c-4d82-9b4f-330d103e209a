


import os
import hashlib
from datetime import datetime
import mysql.connector

# 数据库配置（根据实际情况修改）
# pip3  install mysql-connector-python==8.0.23
db_config = {
    'host': 'localhost',
    'port': 3308,
    'user': 'root',
    'password': 'P@ssw0rd123456',
    'database': 'app1'
}

def calculate_md5(file_path):
    """计算文件的MD5哈希值"""
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return None

def process_jar_files(repository_root, scan_path, conn):
    """处理JAR文件并写入数据库"""
    cursor = conn.cursor()

    # 创建表（如果不存在）
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS ops_file_value (
            jar_name VARCHAR(512),
            oper_time DATETIME,
            md5_value CHAR(32)
        )
    ''')
    conn.commit()

    # 遍历目录查找JAR文件
    for root, dirs, files in os.walk(scan_path):
        for file in files:
            if file.lower().endswith('.jar'):
                jar_path = os.path.join(root, file)

                # 计算相对路径并统一路径分隔符
                relative_path = os.path.relpath(jar_path, repository_root)
                relative_path = relative_path.replace(os.path.sep, '/')

                # 计算MD5
                md5_value = calculate_md5(jar_path)
                if not md5_value:
                    continue  # 跳过计算失败的文件

                # 准备插入数据
                oper_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                try:
                    cursor.execute(
                        "INSERT INTO ops_file_value (jar_name, oper_time, md5_value) VALUES (%s, %s, %s)",
                        (relative_path, oper_time, md5_value)
                    )
                    conn.commit()
                except mysql.connector.Error as e:
                    print(f"Database error: {e}")
                    conn.rollback()

    cursor.close()

def main():
    # Maven仓库根目录
    repository_root = '/root/.m2/repository'
    # 要扫描的目录（相对于仓库根目录）
    scan_path = os.path.join(repository_root, 'com/foxnicweb')

    try:
        # 连接数据库
        conn = mysql.connector.connect(**db_config)
        process_jar_files(repository_root, scan_path, conn)
    except mysql.connector.Error as e:
        print(f"Database connection failed: {e}")
    finally:
        if 'conn' in locals() and conn.is_connected():
            conn.close()

if __name__ == '__main__':
    main()