server:
  session:
    timeout: 12000s
  servlet:
    session:
      # 会话超时时间，如 30s , 20m 等
      timeout: 12000s
  #应用web访问端口,可按照实际情况进行修改
  port: APP_WEB_PORT

spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: service-all
    description: Foxnic Web Service All
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 200MB
  datasource:
    # Druid 监控 , 访问 ${url-prefix}/login.html
    monitor:
      url-prefix: druid
      username: root
      password: root
      #allow: **************,127.0.0.1
      #deny: ************
      resetEnable: true
    druid:
      primary:
        encrypt:
          #数据库连接信息url,username,password三个属性是否加密，如果enable为true，则加密，加密因子从file指定的路径获取
          enable: false
          file:
            windows: d:/foxnic/passwd.txt
            mac: /Users/<USER>/foxnic/passwd.txt
            linux: /foxnic/passwd.txt
        driver-class-name: com.mysql.cj.jdbc.Driver
#        url: ai+mCm8asbK9SNrQDk7ac29neidjdgHp2jFU7HYbZZOmvbR4OKNXb877AJzdYToBGLgN+etaRMM4wzovFLMULMH6brIo3zgsyub026w4Il4Oqg9jzyYsLaa6LNyznmYm5kKoS17K9XHBX8e0lDuik9pcc/DfdjaUkhm0MmOALUM86dHiyURHUP5hfQ4otf/kE9gVP+GO1G7fUdc4JBLDYylfBzmp9SNKQcTLQ/5AkWcnbm89+5pAz4JNnCJILF0B
#        username: tD5wkJAfnCt04A+Y+QN5GA==
#        password: ODGx5F84xpWJgfRFnzAaYhViHJqrTO3uxxDZ2lRlZNk=
        url: ***********************************************************************************************************************************************************************************************
        username: APP_DB_USERNAME
        password: APP_DB_PASSWORD
        # 启动程序时，在连接池中初始化多少个连接
        initialSize: 16
        # 连接池中最多支持多少个活动会话
        maxActive: 128
        # 程序向连接池中请求连接时,超过maxWait的值后，认为本次请求失败，即连接池没有可用连接，单位毫秒，设置-1时表示无限等待
        maxWait: 3000
        # 池中某个连接的空闲时长达到 N 毫秒后, 连接池在下次检查空闲连接时，将回收该连接,要小于防火墙超时设置
        minEvictableIdleTimeMillis: 30000
        # 检查空闲连接的频率，单位毫秒, 非正整数时表示不进行检查程序没有close连接且空闲时长超过 minEvictableIdleTimeMillis,则会执行validationQuery指定的SQL,以保证该程序连接不会池kill掉,其范围不超过minIdle指定的连接个数。
        timeBetweenEvictionRunsMillis: 10000
        # 回收空闲连接时，将保证至少有minIdle个连接.
        minIdle: 8
        # 要求程序从池中get到连接后, N 秒后必须close,否则druid 会强制回收该连接,不管该连接中是活动还是空闲。
        removeAbandoned: false
        # 设置druid 强制回收连接的时限，当程序从池中get到连接开始算起，超过此值后，druid将强制回收该连接，单位秒。
        removeAbandonedTimeout: 86400000
        # 当druid强制回收连接后，是否将stack trace 记录到日志中
        logAbandoned: true
        # 当程序请求连接，池在分配连接时，是否先检查该连接是否有效。(高效)
        testWhileIdle: true
        # 用来保持连接有效性的，只有空闲时间大于keepAliveBetweenTimeMillis并且小于minEvictableIdleTimeMillis该参数才会有用
        keepAlive: true
        # 检查池中的连接是否仍可用的 SQL 语句,drui会连接到数据库执行该SQL, 如果正常返回，则表示连接可用，否则表示连接不可用
        validationQuery: select 1
        # 程序 申请 连接时,进行连接有效性检查（低效，影响性能）
        testOnBorrow: false
        # 程序 返还 连接时,进行连接有效性检查（低效，影响性能）
        testOnReturn: false
        # 是否缓存preparedStatement，也就是PSCache。PSCache对支持游标的数据库性能提升巨大，比如说oracle。在mysql下建议关闭。
        poolPreparedStatements: false
        # 要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true。在Druid中，不会存在Oracle下PSCache占用内存过多的问题，可以把这个数值配置大一些，比如说100
        maxOpenPreparedStatements: -1
        # 要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true。在Druid中，不会存在Oracle下PSCache占用内存过多的问题，可以把这个数值配置大一些，比如说100
        maxPoolPreparedStatementPerConnectionSize: -1
        # 是否打印语句，生产环境建议关闭
        printSQL: true
  thymeleaf:
    prefix: classpath:/public/
    check-template-location: true
    suffix: .html
    encoding: UTF-8
    content-type: text/html
    mode: HTML5
    cache: false
      #session:
      #store-type: redis # 使用 redis 存储session
      #redis:
      #port: 6379
    #host: 127.0.0.1


foxnic:
  api:
    log-enable: true
  config:
    # 按顺序读取 profile 下的配置值，默认使用 default Profile 的配置值，如果位于靠前的 profile 已经有配置，则不再往后读取，即前面 profile 的覆盖后面的 profile ;
    profiles: empty
  # 设置 API 接口
  job:
    enable: true
    force-run-job-ids:
    # 指定 Quartz 使用的DAO数据源
    dao-bean-name: primaryDAO
    # 容许的最大作业延长时间，超出则认为 misfire 发生，单位毫秒
    misfire-threshold: 1000
    # 日志保留天数
    log-keep-days: 7
  storage:
    mode: disk
    disk:
      location:
        #在不同的环境下选择不同的路径
        windows: c:/uploads
        mac: /Users/<USER>/upload
        linux: APP_UPLOAD_DIR
  cache:
    # 默认配置
    default:
      # local,remote,both,none
      mode: both
      # 本地缓存配置 : 最大元素个数
      local-limit: 10240
      # 本地缓存配置 : 本地缓存超时时长；-1 表示用不过期
      local-expire: 1200000
      # 远程缓存配置 :  超时 20 分钟，单位毫秒; -1 表示用不过期
      remote-expire: 1200000
    # 按实体类型配置缓存细节
    details:
      "org.github.foxnic.web.domain.system.Config":
        strategy:
          by-code:
            is-accurate: true
            cache-empty-result: true
            properties: code
      "org.github.foxnic.web.domain.oauth.Resourze":
        #mode: local
        local-limit: 10240
        local-expire: 1200000
        remote-expire: 1200000
      "org.github.foxnic.web.domain.system.DictItem":
        #mode: local
        local-limit: 10240
        local-expire: 1200000
        remote-expire: 1200000
        strategy:
          query-list:
            is-accurate: true
            cache-empty-result: true
            properties: dictCode
      "org.github.foxnic.web.domain.system.DbCache":
        #mode: local
        local-limit: 10240
        local-expire: 1200000
        remote-expire: 1200000
        strategy:
          query-list:
            is-accurate: true
            cache-empty-result: true
            properties: area,catalog,ownerType,ownerId
      "org.github.foxnic.web.domain.pcm.Catalog":
        #mode: local
        local-limit: 10240
        local-expire: 1200000
        remote-expire: 1200000
  # 配置集群节点简单替代注册中心，如果启用微服务，则一下配置无效
  cluster:
    # 节点间用于通信的 key，各节点要保持一致
    key: FOXNIC-EAM-20220527-9494
    service:
      service-camunda:
        enable: true
        # 多个节点时用逗号隔开,格式： 地址,权重 | 地址,权重 | ...
        end-points: http://127.0.0.1:8099,1 | http://127.0.0.1:8099,1
      service-hrm:
        enable: true
        # 多个节点时用逗号隔开,格式： 地址,权重 | 地址,权重 | ...
        end-points: http://127.0.0.1:8089,1 | http://127.0.0.1:8089,2
      service-oauth:
        enable: true
        # 多个节点时用逗号隔开,格式： 地址,权重 | 地址,权重 | ...
        end-points: http://127.0.0.1:8089,1 | http://127.0.0.1:8089,2
      service-system:
        enable: true
        # 多个节点时用逗号隔开,格式： 地址,权重 | 地址,权重 | ...
        end-points: http://127.0.0.1:8089,1 | http://127.0.0.1:8089,2
      service-storage:
        enable: true
        # 多个节点时用逗号隔开,格式： 地址,权重 | 地址,权重 | ...
        end-points: http://127.0.0.1:8089,1 | http://127.0.0.1:8089,2

#禁止自动扫描API
springfox:
  documentation:
    auto-startup: false


security:
  # 模式：session / jwt
  mode: both
  # 配置无需鉴权可以访问的地址
  ignored-urls: /assets/** , /pages/** , /*.ico , /module/** ,  /**/*.css , /**/*.js , /error , /druid/**,/business/eam/user_book/**
  # mode=session 时配置的登录页面
  login-page: /login.html
  jwt:
    key-location: foxnic-web.jks
    key-alias: foxnic-web
    key-pass: fox-123456-nic
    # 缓存类型  local/remote/both
    cache-type: local
    iss: felord.cn
    sub: all
    access-expire-seconds: 1800
    refresh-expire-seconds: 2000


logging:
  config: classpath:logback-config.xml

snowflake:
  datacenterId: 1
  workerId: 1

management:
  endpoints:
    web:
      exposure:
        include: "*"

knife4j:
  enable: true
  title: ${spring.application.name}
  description: ${spring.application.description}
  version: 1.0



develop:
  language: defaults
  start-relation-monitor: true
  deploy:
    enable: true
    # 可以使用以当前项目为基础的相对路径，指定静态资源自动部署的项目目录
    projects:  ../view/view-workorder,../view/view-cms,../view/view-vehicle,../view/view-customer,../view/view-contract,../view/view-knowledgebase, ../view/view-common, ../view/view-console, ../view/view-datacenter, ../view/view-eam, ../view/view-hrm, ../view/view-ops
