#!/bin/sh
cur_dir=$(cd `dirname $0`; pwd)
app_dir=$cur_dir/..
echo "cur_dir:$cur_dir"
prod_app_dir=/app/app
app_conf="${cur_dir}/app.conf"

logfile="$prod_app_dir/logs/app_update.log";
mkdir -p $prod_app_dir/logs
if [[ ! -d $prod_app_dir/logs ]];then
  echo "$prod_app_dir/logs not exists"
  exit 1
fi

nowTime=`date +%Y$m%d%H%M%S`
echo "start to update app,time:$nowTime">>$logfile
tpl_update_par_cnt=`cat $app_conf|grep -v "#"|grep APP_TPL_UPDATE=|wc -l`
if [[ $tpl_update_par_cnt -eq 0 ]];then
  tpl_update=0
else
  tpl_update=`cat $app_conf|grep -v "#"|grep APP_TPL_UPDATE=|awk -F "=" '{print $2}'`
fi
echo "tpl_update value:$tpl_update"
if [[ ! -d "$prod_app_dir/app/lib" ]];then
  echo "directory config error"
  exit 1
fi

#app.jar will overwrite if exist
echo "######## start to update app.jar ##############"
echo "start to update app jar">>$logfile
if [[ -f "$prod_app_dir/app/app.jar" ]];then
  rm -rf $prod_app_dir/app/app.jar
  echo "cp $app_dir/app/app.jar $prod_app_dir/app/"
  cp $app_dir/app/app.jar $prod_app_dir/app/
  ls -rtl $prod_app_dir/app/
else
  echo "app.jar not exist"
  echo "cp $app_dir/app/app.jar $prod_app_dir/app/"
  cp $app_dir/app/app.jar $prod_app_dir/app/
fi
echo ""
#overwrite yml tpl file
cd $app_dir/app
echo "cp $prod_app_dir/app/application_tpl.yml"
if [[ -f "$prod_app_dir/app/application_tpl.yml" ]];then
  rm -rf $prod_app_dir/app/application_tpl.yml
fi
cp application_tpl.yml $prod_app_dir/app/
echo ""

#app lib will copy
echo " ########start to update app lib ##############"
echo "start to update app lib">>$logfile
if [[ -d $prod_app_dir/app/lib ]];then
  rm -rf $prod_app_dir/app/lib/*
  cd $app_dir/app/lib/
  echo "cp * $prod_app_dir/app/lib"
  cp * $prod_app_dir/app/lib/
  ls -rtl $prod_app_dir/app/lib/*
else
  echo "$prod_app_dir/app/lib  error"
  exit 1
fi
echo ""
echo ""

#bpm.jar will overwrite
echo "######## start to update bpm.jar ##############"
echo "start to update bpm jar">>$logfile
if [[ -f "$prod_app_dir/bpm/bpm.jar" ]];then
  cd $app_dir/bpm
  echo "cp bpm.jar $prod_app_dir/bpm/"
  cp bpm.jar $prod_app_dir/bpm/
  ls -rtl $prod_app_dir/bpm/
else
  echo "bpm.jar error"
  exit 1
fi
echo ""
#overwrite yml tpl file
cd $app_dir/bpm
echo "cp $prod_app_dir/bpm/application_tpl.yml"
if [[ -f "$prod_app_dir/bpm/application_tpl.yml" ]];then
  rm -rf $prod_app_dir/bpm/application_tpl.yml
fi
cp application_tpl.yml $prod_app_dir/bpm/
echo ""

#sh file will overwrite
echo "######## start to update script file ##############"
echo "start to update script file">>$logfile
cd $app_dir/bin/
scriptFileList=`ls -rtl |grep ".sh"|awk '{print $NF}'`
for scriptFile in $scriptFileList
do
  echo "script:$scriptFile"
  if [[ -f "$prod_app_dir/bin/$scriptFile" ]];then
    rm -rf $prod_app_dir/bin/$scriptFile
  fi
  cp $scriptFile $prod_app_dir/bin/
done
chmod +x $prod_app_dir/bin/*.sh
ls -rtl $prod_app_dir/bin/*.sh

#Tpl File
echo "######## start to update tpl file ##############"
echo "start to update tpl file">>$logfile
if [[ $tpl_update -eq 1 ]];then
  echo "start to overwrite tpl file"
  if [[ -d "$prod_app_dir/app/upload/tpl/T001" ]];then
    t=`date +%Y%m%d_%H_%M_%S`
    mv $prod_app_dir/app/upload/tpl/T001 $prod_app_dir/app/upload/tpl/T001_$t
    mkdir -p $prod_app_dir/app/upload/tpl/T001
    cd $app_dir/app/upload/tpl/T001
    cp * $prod_app_dir/app/upload/tpl/T001
    ls -rtl $prod_app_dir/app/upload/tpl/T001
  else
     echo "$prod_app_dir/app/upload/tpl/T001 error"
     exit 1
  fi
fi


#docs
if [[ -d "$prod_app_dir/app/upload/docs/T001" ]];then
  cd $prod_app_dir/app/upload/docs/T001
  rm -rf *
  cd $app_dir/app/upload/docs/T001
  echo "cp -r * $prod_app_dir/app/upload/docs/T001"
  cp -r * $prod_app_dir/app/upload/docs/T001
fi



#app sql
if [[ -d "$prod_app_dir/bin/sql" ]];then
  cd $prod_app_dir/bin/sql
  rm -rf *
  cd $app_dir/bin/sql
  echo "cp -r * $prod_app_dir/bin/sql"
  cp -r * $prod_app_dir/bin/sql
fi

#docker file
if [[ -d "$prod_app_dir/docker" ]];then
  cd $prod_app_dir/docker
  rm -rf *
  cd $app_dir/docker
  echo "cp -r * $prod_app_dir/docker"
  cp -r * $prod_app_dir/docker
fi

#app sql
if [[ -d "$prod_app_dir/bin/sql" ]];then
  cd $prod_app_dir/bin/sql
  rm -rf *
  cd $app_dir/bin/sql
  echo "cp -r * $prod_app_dir/bin/sql"
  cp -r * $prod_app_dir/bin/sql
fi

#restart
echo "######## restart app ##############"
cd $prod_app_dir
echo "next restart all app">>$logfile
#sh restartAll.sh
echo "update finish">>$logfile
exit 0
