select * from (
                  SELECT
                      curr.jar_name,
                      curr.oper_time AS change_time,
                      prev.md5_value AS old_md5,
                      curr.md5_value AS new_md5,
                      COALESCE(prev.md5_value, '') = COALESCE(curr.md5_value, '') as diff_res
                  FROM ops_file_value curr
                           LEFT JOIN ops_file_value prev ON
                              curr.jar_name = prev.jar_name
                          AND prev.oper_time = (
                          SELECT MAX(oper_time)
                          FROM ops_file_value

                          WHERE jar_name = curr.jar_name
                            AND oper_time < curr.oper_time
                      ) where prev.md5_value is not null
              ) end where diff_res=0 order by jar_name